'use client';

import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'elevated' | 'interactive' | 'modern' | 'gradient' | 'feature';
  children: React.ReactNode;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', children, ...props }, ref) => {
    const variants = {
      default: 'bg-white border border-gray-200 shadow-soft',
      glass: 'glass-card',
      elevated: 'bg-white shadow-large border border-gray-100',
      interactive: 'interactive-card bg-white border border-gray-200 shadow-soft cursor-pointer hover:shadow-glow transition-all duration-500',
      modern: 'glass-strong border border-white/20 shadow-colored hover:shadow-glow-lg transition-all duration-500 hover:scale-[1.02]',
      gradient: 'bg-gradient-to-br from-white/80 to-white/40 border border-white/20 shadow-glow backdrop-blur-xl',
      feature: 'glass-subtle border border-white/10 shadow-medium hover:glass-strong hover:shadow-glow transition-all duration-500 hover:scale-[1.03] hover:-translate-y-2 group cursor-pointer'
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div
          ref={ref}
          className={cn('rounded-2xl p-6', variants[variant], className)}
          {...props}
        >
          {children}
        </div>
      </motion.div>
    );
  }
);

Card.displayName = 'Card';

const CardHeader = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('mb-4', className)} {...props} />
  )
);
CardHeader.displayName = 'CardHeader';

const CardTitle = forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, style, ...props }, ref) => (
    <h3 ref={ref} className={cn('text-heading-lg', className)} style={{ color: 'var(--linear-text-primary)', ...style }} {...props} />
  )
);
CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, style, ...props }, ref) => (
    <p ref={ref} className={cn('text-body', className)} style={{ color: 'var(--linear-text-secondary)', ...style }} {...props} />
  )
);
CardDescription.displayName = 'CardDescription';

const CardContent = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('', className)} {...props} />
  )
);
CardContent.displayName = 'CardContent';

const CardFooter = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('mt-6 pt-6 border-t border-gray-100', className)} {...props} />
  )
);
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
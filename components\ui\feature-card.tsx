import { ReactNode } from 'react';
import { GlassCard } from './glass-card';
import { cn } from '@/lib/utils';

interface FeatureCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  className?: string;
}

export function FeatureCard({ icon, title, description, className }: FeatureCardProps) {
  return (
    <GlassCard className={cn('p-8 group hover:scale-105 transition-all duration-300', className)}>
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500/20 to-indigo-500/20 group-hover:from-blue-500/30 group-hover:to-indigo-500/30 transition-colors duration-300">
          {icon}
        </div>
        <h3 className="text-xl font-bold" style={{ color: 'var(--linear-text-primary)' }}>{title}</h3>
        <p className="leading-relaxed" style={{ color: 'var(--linear-text-secondary)' }}>{description}</p>
      </div>
    </GlassCard>
  );
}
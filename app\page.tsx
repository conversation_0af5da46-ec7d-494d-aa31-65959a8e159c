'use client';

import { useEffect, Suspense, lazy, useState, useCallback } from 'react';
import Hero from '@/components/sections/hero-professional';
import Footer from '@/components/layout/footer-professional';

// Optimized lazy loading with intelligent preloading
const Features = lazy(() => import('@/components/sections/features-professional'));
const DashboardShowcase = lazy(() => import('@/components/sections/dashboard-showcase'));
const Pricing = lazy(() => import('@/components/sections/pricing-professional'));
const Testimonials = lazy(() => import('@/components/sections/testimonials-linear'));
const FAQ = lazy(() => import('@/components/sections/faq-linear'));
const Contact = lazy(() => import('@/components/sections/contact-linear'));

// Preload components when user scrolls near them
const preloadComponent = (importFn: () => Promise<any>) => {
  importFn();
};

// Optimized loading skeleton component
const LoadingSkeleton = () => (
  <div className="section-spacing" style={{ background: 'var(--linear-bg-primary)' }}>
    <div className="content-container">
      <div className="animate-pulse">
        <div className="h-8 rounded-md w-1/3 mx-auto mb-4" style={{ background: 'var(--linear-bg-secondary)' }}></div>
        <div className="h-4 rounded-md w-2/3 mx-auto mb-8" style={{ background: 'var(--linear-bg-secondary)' }}></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-64 rounded-lg" style={{ background: 'var(--linear-bg-secondary)' }}></div>
          ))}
        </div>
      </div>
    </div>
  </div>
);


export default function Home() {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  // Optimized scroll tracking with throttling and intelligent preloading
  const handleScroll = useCallback(() => {
    if (!isScrolling) {
      requestAnimationFrame(() => {
        const newScrollY = window.scrollY;
        setScrollY(newScrollY);

        // Intelligent preloading based on scroll position
        const windowHeight = window.innerHeight;
        const preloadThreshold = windowHeight * 0.5; // Preload when 50% away

        // Preload Features section when approaching
        if (newScrollY > windowHeight - preloadThreshold) {
          preloadComponent(() => import('@/components/sections/features-professional'));
        }

        // Preload Dashboard section when approaching
        if (newScrollY > windowHeight * 1.2 - preloadThreshold) {
          preloadComponent(() => import('@/components/sections/dashboard-showcase'));
        }

        // Preload Pricing section when approaching
        if (newScrollY > windowHeight * 1.8 - preloadThreshold) {
          preloadComponent(() => import('@/components/sections/pricing-professional'));
        }

        // Preload Testimonials section when approaching
        if (newScrollY > windowHeight * 2.8 - preloadThreshold) {
          preloadComponent(() => import('@/components/sections/testimonials-linear'));
        }

        setIsScrolling(false);
      });
      setIsScrolling(true);
    }
  }, [isScrolling]);

  // Scroll progress calculation
  const scrollProgress = typeof window !== 'undefined' ? Math.min(
    scrollY / (document.documentElement.scrollHeight - window.innerHeight),
    1
  ) : 0;

  useEffect(() => {
    // Add scroll listener for performance tracking
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Optimized intersection observer for scroll reveals
    const observerOptions = {
      threshold: [0.1, 0.3, 0.5],
      rootMargin: '0px 0px -100px 0px'
    };

    // High-performance intersection observer
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');

          // Optimized stagger animations
          const children = entry.target.querySelectorAll('.stagger-child');
          children.forEach((child, index) => {
            setTimeout(() => {
              child.classList.add('revealed');
            }, index * 100);
          });

          // Unobserve after revealing to improve performance
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements for scroll reveals
    const revealElements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right');
    revealElements.forEach((element) => {
      observer.observe(element);
    });

    // Preload next sections when user scrolls
    const preloadSections = () => {
      if (scrollY > window.innerHeight * 0.5) {
        // Preload features section
        import('@/components/sections/features-professional');
      }
      if (scrollY > window.innerHeight * 1.2) {
        // Preload dashboard section
        import('@/components/sections/dashboard-showcase');
      }
      if (scrollY > window.innerHeight * 1.8) {
        // Preload pricing section
        import('@/components/sections/pricing-professional');
      }
      if (scrollY > window.innerHeight * 2.8) {
        // Preload testimonials section
        import('@/components/sections/testimonials-linear');
      }
    };

    // Throttled preloading
    let preloadTicking = false;
    const handlePreload = () => {
      if (!preloadTicking) {
        requestAnimationFrame(() => {
          preloadSections();
          preloadTicking = false;
        });
        preloadTicking = true;
      }
    };

    window.addEventListener('scroll', handlePreload, { passive: true });

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('scroll', handlePreload);
    };
  }, [handleScroll, scrollY]);

  return (
    <div className="min-h-screen scroll-optimized" style={{ background: 'var(--linear-bg-primary)' }}>
      {/* Optimized Scroll Progress Indicator */}
      <div
        className="scroll-progress"
        style={{
          transform: `scaleX(${scrollProgress})`,
          transformOrigin: '0%'
        }}
      />

      {/* Scroll to top button */}
      {scrollY > 500 && (
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 z-50 flex items-center justify-center"
          aria-label="Scroll to top"
        >
          ↑
        </button>
      )}

      {/* Main Content - Optimized for performance */}
      <main>
        <section id="hero" className="scroll-reveal">
          <Hero />
        </section>

        {/* Optimized lazy loaded sections with better loading states */}
        <section id="features" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <Features />
          </Suspense>
        </section>

        <section id="dashboard" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <DashboardShowcase />
          </Suspense>
        </section>

        <section id="pricing" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <Pricing />
          </Suspense>
        </section>

        <section id="testimonials" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <Testimonials />
          </Suspense>
        </section>

        <section id="faq" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <FAQ />
          </Suspense>
        </section>

        <section id="contact" className="scroll-reveal">
          <Suspense fallback={<LoadingSkeleton />}>
            <Contact />
          </Suspense>
        </section>
      </main>

      <Footer />
    </div>
  );
}
'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Building2, 
  Monitor, 
  Smartphone, 
  Newspaper,
  Radio,
  Tv,
  Globe,
  ArrowRight,
  Sparkles,
  Zap
} from 'lucide-react';

const integrations = [
  {
    name: '8Hoarding',
    emoji: '🏢',
    category: 'Outdoor Media',
    description: 'Premium hoarding and billboard advertising solutions across major cities',
    color: 'from-blue-500 to-cyan-500',
    icon: Building2,
    position: { x: -2, y: -1, z: 1 }
  },
  {
    name: 'Ad8OOH',
    emoji: '📺',
    category: 'Out-of-Home',
    description: 'Comprehensive out-of-home advertising network and management platform',
    color: 'from-purple-500 to-pink-500',
    icon: Monitor,
    position: { x: 2, y: -1, z: 0 }
  },
  {
    name: '8Digiad',
    emoji: '💻',
    category: 'Digital Advertising',
    description: 'Advanced digital advertising campaigns with real-time optimization',
    color: 'from-green-500 to-emerald-500',
    icon: Monitor,
    position: { x: 0, y: -2, z: -1 }
  },
  {
    name: 'Ad8Mobi',
    emoji: '📱',
    category: 'Mobile Advertising',
    description: 'Mobile-first advertising solutions for app and web campaigns',
    color: 'from-orange-500 to-red-500',
    icon: Smartphone,
    position: { x: -1, y: 1, z: 2 }
  },
  {
    name: 'Ad8Paper',
    emoji: '📰',
    category: 'Print Media',
    description: 'Traditional print advertising across newspapers and magazines',
    color: 'from-indigo-500 to-purple-500',
    icon: Newspaper,
    position: { x: 1, y: 2, z: -1 }
  },
  {
    name: 'Ad8Radio',
    emoji: '📻',
    category: 'Radio Advertising',
    description: 'Radio advertising campaigns across multiple stations and regions',
    color: 'from-yellow-500 to-orange-500',
    icon: Radio,
    position: { x: -2, y: 0, z: -1 }
  },
  {
    name: 'Ad8TV',
    emoji: '📺',
    category: 'Television',
    description: 'Television advertising campaigns with audience targeting and analytics',
    color: 'from-red-500 to-pink-500',
    icon: Tv,
    position: { x: 2, y: 1, z: 1 }
  },
  {
    name: 'Ad8Social',
    emoji: '🌐',
    category: 'Social Media',
    description: 'Social media advertising across all major platforms and networks',
    color: 'from-cyan-500 to-blue-500',
    icon: Globe,
    position: { x: 0, y: 2, z: 0 }
  }
];

const categories = ['All', 'Outdoor Media', 'Digital Advertising', 'Mobile Advertising', 'Print Media', 'Radio Advertising', 'Television', 'Social Media'];

export default function Integrations3D() {
  const [activeCategory, setActiveCategory] = useState('All');
  const [hoveredIntegration, setHoveredIntegration] = useState<string | null>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const filteredIntegrations = activeCategory === 'All' 
    ? integrations 
    : integrations.filter(integration => integration.category === activeCategory);

  return (
    <section 
      ref={sectionRef} 
      className="section-spacing scroll-optimized transform-gpu will-change-transform relative overflow-hidden" 
      style={{ background: 'var(--linear-bg-primary)' }}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-green-500/10 to-cyan-500/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl" />
      </div>

      <div className="content-container relative z-10">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge purple mb-6"
          >
            <Sparkles className="w-4 h-4" />
            Platform Integrations
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Complete Advertising Ecosystem
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="linear-text-lg max-w-3xl mx-auto"
            style={{ color: 'var(--linear-text-secondary)' }}
          >
            Access India's most comprehensive advertising platform ecosystem. From outdoor hoardings to digital campaigns, manage all your advertising channels in one unified platform.
          </motion.p>
        </div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-16"
        >
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeCategory === category 
                  ? 'bg-white/10 border border-white/20' 
                  : 'hover:bg-white/5'
              }`}
              style={{ 
                color: activeCategory === category 
                  ? 'var(--linear-text-primary)' 
                  : 'var(--linear-text-secondary)' 
              }}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* 3D Integration Grid */}
        <div className="relative min-h-[700px] flex items-center justify-center">
          {/* Central Hub */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="absolute z-20"
          >
            <div 
              className="w-32 h-32 rounded-3xl flex items-center justify-center relative"
              style={{ background: 'var(--linear-bg-secondary)', border: '2px solid var(--linear-border)' }}
            >
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                <Zap className="w-10 h-10 text-white" />
              </div>
              
              {/* Pulsing Rings */}
              <motion.div
                animate={{ scale: [1, 1.3, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute inset-0 rounded-3xl border-2 border-blue-500/30"
              />
              <motion.div
                animate={{ scale: [1, 1.5, 1], opacity: [0.3, 0, 0.3] }}
                transition={{ duration: 3, repeat: Infinity, delay: 1 }}
                className="absolute inset-0 rounded-3xl border-2 border-purple-500/20"
              />
            </div>
          </motion.div>

          {/* Integration Cards in 3D Layout */}
          <div className="absolute inset-0">
            {filteredIntegrations.map((integration, index) => {
              const Icon = integration.icon;
              const delay = 0.5 + index * 0.1;
              const angle = (index / filteredIntegrations.length) * 2 * Math.PI;
              const radius = 280;
              const x = Math.cos(angle) * radius;
              const y = Math.sin(angle) * radius;
              
              return (
                <motion.div
                  key={integration.name}
                  initial={{ opacity: 0, scale: 0.5, rotateY: -90 }}
                  whileInView={{ opacity: 1, scale: 1, rotateY: 0 }}
                  transition={{ duration: 0.8, delay }}
                  viewport={{ once: true }}
                  className="absolute top-1/2 left-1/2 cursor-pointer group"
                  style={{
                    transform: `translate(-50%, -50%) translate3d(${x + integration.position.x * 30}px, ${y + integration.position.y * 30}px, ${integration.position.z * 20}px)`,
                    transformStyle: 'preserve-3d'
                  }}
                  onMouseEnter={() => setHoveredIntegration(integration.name)}
                  onMouseLeave={() => setHoveredIntegration(null)}
                >
                  {/* Connection Line to Center */}
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    transition={{ duration: 1, delay: delay + 0.3 }}
                    viewport={{ once: true }}
                    className="absolute top-1/2 left-1/2 h-0.5 bg-gradient-to-r from-white/20 via-white/40 to-transparent origin-left pointer-events-none z-0"
                    style={{
                      width: `${Math.sqrt(x * x + y * y)}px`,
                      transform: `translate(-50%, -50%) rotate(${Math.atan2(-y, -x) * 180 / Math.PI}deg)`
                    }}
                  />

                  {/* Integration Card */}
                  <motion.div
                    whileHover={{ 
                      scale: 1.1, 
                      rotateY: 10,
                      z: 100
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="linear-card p-6 rounded-2xl text-center relative overflow-hidden w-48 z-10"
                    style={{ 
                      background: 'var(--linear-bg-secondary)', 
                      border: '1px solid var(--linear-border)',
                      transformStyle: 'preserve-3d'
                    }}
                  >
                    {/* Gradient Overlay */}
                    <div 
                      className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 bg-gradient-to-br ${integration.color}`}
                    />
                    
                    {/* Emoji and Icon */}
                    <div className="relative mb-4">
                      <div className="text-4xl mb-2">{integration.emoji}</div>
                      <div 
                        className={`w-12 h-12 mx-auto rounded-xl flex items-center justify-center bg-gradient-to-br ${integration.color}`}
                      >
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    
                    {/* Content */}
                    <h3 className="font-bold text-lg mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                      {integration.name}
                    </h3>
                    <p className="text-xs mb-3 font-medium" style={{ color: 'var(--linear-text-muted)' }}>
                      {integration.category}
                    </p>
                    <p className="text-sm leading-relaxed" style={{ color: 'var(--linear-text-secondary)' }}>
                      {integration.description}
                    </p>

                    {/* Floating particles on hover */}
                    {hoveredIntegration === integration.name && (
                      <>
                        {[...Array(4)].map((_, i) => (
                          <motion.div
                            key={i}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ 
                              opacity: [0, 1, 0], 
                              scale: [0, 1, 0],
                              x: [0, (i - 2) * 25],
                              y: [0, -30 - i * 15]
                            }}
                            transition={{ 
                              duration: 2, 
                              repeat: Infinity,
                              delay: i * 0.3
                            }}
                            className="absolute w-1.5 h-1.5 bg-white rounded-full top-1/2 left-1/2"
                          />
                        ))}
                      </>
                    )}

                    {/* Hover Arrow */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      whileHover={{ opacity: 1, y: 0 }}
                      className="absolute bottom-4 right-4"
                    >
                      <ArrowRight className="w-4 h-4" style={{ color: 'var(--linear-text-muted)' }} />
                    </motion.div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {[
              { number: '8+', label: 'Advertising Platforms', desc: 'Complete ecosystem coverage' },
              { number: '10M+', label: 'Ad Inventory Options', desc: 'Across all media channels' },
              { number: '24/7', label: 'Platform Integration', desc: 'Seamless connectivity' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 + index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl font-bold mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                  {stat.number}
                </div>
                <div className="text-lg font-medium mb-1" style={{ color: 'var(--linear-text-secondary)' }}>
                  {stat.label}
                </div>
                <div className="text-sm" style={{ color: 'var(--linear-text-muted)' }}>
                  {stat.desc}
                </div>
              </motion.div>
            ))}
          </div>
          
          <p className="text-lg mb-6" style={{ color: 'var(--linear-text-secondary)' }}>
            Need a custom integration? Our API supports unlimited platform connections.
          </p>
          <button className="btn-primary-linear">
            Explore API Documentation
          </button>
        </motion.div>
      </div>
    </section>
  );
}

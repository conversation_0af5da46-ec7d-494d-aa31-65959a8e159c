'use client';

import { motion } from 'framer-motion';
import { useMagneticEffect, morphingButton } from '@/lib/advanced-animations';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface MagneticButtonProps {
  children: ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  strength?: number;
  onClick?: () => void;
  disabled?: boolean;
}

export function MagneticButton({
  children,
  className = '',
  variant = 'primary',
  size = 'md',
  strength = 0.3,
  onClick,
  disabled = false,
}: MagneticButtonProps) {
  const { ref, position } = useMagneticEffect(strength);

  const variants = {
    primary: 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-glow hover:shadow-glow-lg',
    secondary: 'glass-strong border border-white/20 text-gray-900 hover:glass-strong',
    outline: 'border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white',
    ghost: 'text-gray-700 hover:bg-gray-100/50',
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  return (
    <motion.button
      ref={ref}
      className={cn(
        'relative font-semibold rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group',
        variants[variant],
        sizes[size],
        className
      )}
      onClick={onClick}
      disabled={disabled}
      animate={{
        x: position.x,
        y: position.y,
      }}
      variants={morphingButton}
      initial="rest"
      whileHover="hover"
      whileTap="tap"
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20,
      }}
    >
      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-2xl"
        initial={{ scale: 0, opacity: 0 }}
        whileTap={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
      
      {/* Shimmer effect */}
      <div className="absolute inset-0 -top-px overflow-hidden rounded-2xl">
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
          initial={{ x: '-100%' }}
          whileHover={{ x: '100%' }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
        />
      </div>
      
      {/* Content */}
      <span className="relative z-10">{children}</span>
    </motion.button>
  );
}

export function FloatingActionButton({
  children,
  className = '',
  onClick,
}: {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
}) {
  const { ref, position } = useMagneticEffect(0.2);

  return (
    <motion.button
      ref={ref}
      className={cn(
        'fixed bottom-8 right-8 w-14 h-14 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full shadow-glow flex items-center justify-center text-white z-50',
        className
      )}
      onClick={onClick}
      animate={{
        x: position.x,
        y: position.y,
      }}
      whileHover={{
        scale: 1.1,
        boxShadow: "0 0 40px rgba(139, 92, 246, 0.5)",
      }}
      whileTap={{ scale: 0.9 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20,
      }}
    >
      <motion.div
        animate={{
          rotate: [0, 360],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
      >
        {children}
      </motion.div>
    </motion.button>
  );
}

export function PulsingButton({
  children,
  className = '',
  onClick,
}: {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
}) {
  return (
    <motion.button
      className={cn(
        'relative px-8 py-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold rounded-2xl overflow-hidden',
        className
      )}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {/* Pulsing background */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-purple-400 to-indigo-400"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      
      {/* Content */}
      <span className="relative z-10">{children}</span>
    </motion.button>
  );
}

export function GlitchButton({
  children,
  className = '',
  onClick,
}: {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
}) {
  return (
    <motion.button
      className={cn(
        'relative px-6 py-3 bg-black text-white font-mono font-bold rounded-lg overflow-hidden group',
        className
      )}
      onClick={onClick}
      whileHover="hover"
      whileTap={{ scale: 0.95 }}
    >
      {/* Glitch layers */}
      <motion.span
        className="absolute inset-0 bg-red-500 mix-blend-multiply"
        variants={{
          hover: {
            x: [0, -2, 2, 0],
            opacity: [0, 0.7, 0.7, 0],
          },
        }}
        transition={{ duration: 0.3, repeat: Infinity }}
      />
      
      <motion.span
        className="absolute inset-0 bg-cyan-500 mix-blend-multiply"
        variants={{
          hover: {
            x: [0, 2, -2, 0],
            opacity: [0, 0.7, 0.7, 0],
          },
        }}
        transition={{ duration: 0.3, repeat: Infinity, delay: 0.1 }}
      />
      
      {/* Content */}
      <span className="relative z-10">{children}</span>
    </motion.button>
  );
}

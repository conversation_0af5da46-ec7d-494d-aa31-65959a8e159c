'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Quote, Award, TrendingUp, Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const testimonials = [
  {
    name: "<PERSON>",
    role: "Marketing Director",
    company: "TechFlow Inc",
    companySize: "500+ employees",
    industry: "SaaS",
    content: "ADmyBRAND transformed our marketing completely. We've seen a 250% increase in qualified leads and our content creation time has been cut by 80%. The AI insights are incredibly accurate.",
    avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "250%", metric: "qualified leads" },
    featured: true
  },
  {
    name: "<PERSON>",
    role: "CEO",
    company: "GrowthLab",
    companySize: "50-200 employees",
    industry: "Marketing Agency",
    content: "The predictive analytics helped us identify our best customer segments before our competitors. Our ROI improved by 300% in just 6 months. This is the future of marketing.",
    avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "300%", metric: "ROI improvement" },
    featured: false
  },
  {
    name: "Emily Watson",
    role: "Brand Manager",
    company: "StyleCo",
    companySize: "200-500 employees",
    industry: "E-commerce",
    content: "The brand voice analysis is phenomenal. It ensures all our content maintains consistency across channels while the automation saves us 20+ hours per week. Absolutely game-changing.",
    avatar: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "20+", metric: "hours saved/week" },
    featured: false
  },
  {
    name: "David Park",
    role: "Growth Hacker",
    company: "StartupXYZ",
    companySize: "10-50 employees",
    industry: "Fintech",
    content: "As a startup, we needed enterprise-level marketing tools at a fraction of the cost. ADmyBRAND delivered exactly that. Our conversion rates doubled in the first month.",
    avatar: "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "2x", metric: "conversion rates" },
    featured: false
  }
];

const companies = [
  { name: "TechFlow", logo: "🚀" },
  { name: "GrowthLab", logo: "📈" },
  { name: "StyleCo", logo: "✨" },
  { name: "StartupXYZ", logo: "💡" },
  { name: "DataCorp", logo: "📊" },
  { name: "CloudTech", logo: "☁️" }
];

const stats = [
  { value: "10,000+", label: "Active Users", icon: Users },
  { value: "250%", label: "Avg ROI Increase", icon: TrendingUp },
  { value: "4.9/5", label: "Customer Rating", icon: Star },
  { value: "99.9%", label: "Uptime", icon: Award }
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const next = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prev = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section id="testimonials" className="section-padding bg-gradient-to-br from-indigo-50/30 via-white to-purple-50/30 relative overflow-hidden" ref={ref}>
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.02)_1px,transparent_1px)] bg-[size:64px_64px]" />

        {/* Floating testimonial bubbles */}
        <motion.div
          className="absolute top-20 left-10 w-16 h-16 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-xl"
          animate={{
            y: [-20, 20, -20],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-12 h-12 bg-gradient-to-r from-cyan-400/10 to-blue-400/10 rounded-xl blur-lg"
          animate={{
            y: [20, -20, 20],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="max-width-content mx-auto container-padding relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="inline-flex items-center space-x-3 glass-subtle rounded-full px-6 py-3 mb-8 border border-white/20"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Star className="h-4 w-4 text-yellow-500 fill-current" />
            <span className="text-sm font-medium text-gray-700">Customer Stories</span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 text-balance"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Loved by marketing teams{' '}
            <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
              worldwide
            </span>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed text-pretty"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            Join thousands of teams who have transformed their marketing with ADmyBRAND's
            AI-powered intelligence and automation.
          </motion.p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="text-center glass-subtle rounded-2xl p-6 border border-white/20 hover:glass-strong transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                className="w-12 h-12 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 flex items-center justify-center mx-auto mb-4"
                whileHover={{ rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <stat.icon className="h-6 w-6 text-white" />
              </motion.div>
              <div className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Testimonial Carousel */}
        <motion.div
          className="relative max-w-5xl mx-auto mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <motion.div
            className="glass-strong rounded-3xl p-8 lg:p-12 border border-white/20 shadow-glow relative overflow-hidden"
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5 rounded-3xl" />
            <div className="absolute top-8 left-8">
              <Quote className="h-12 w-12 text-indigo-200" />
            </div>

            <div className="relative z-10">
              {/* Rating Stars */}
              <motion.div
                className="flex items-center justify-center mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.3 + i * 0.1 }}
                  >
                    <Star className="h-6 w-6 text-yellow-400 fill-current mx-1" />
                  </motion.div>
                ))}
              </motion.div>

              {/* Testimonial Content */}
              <motion.blockquote
                className="text-xl lg:text-2xl text-gray-900 text-center mb-8 leading-relaxed font-medium text-pretty"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                "{testimonials[currentIndex].content}"
              </motion.blockquote>

              {/* Customer Info */}
              <motion.div
                className="flex flex-col lg:flex-row items-center justify-center space-y-4 lg:space-y-0 lg:space-x-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <div className="flex items-center space-x-4">
                  <motion.img
                    src={testimonials[currentIndex].avatar}
                    alt={testimonials[currentIndex].name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-medium"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  />
                  <div className="text-center lg:text-left">
                    <div className="font-semibold text-gray-900 text-lg">
                      {testimonials[currentIndex].name}
                    </div>
                    <div className="text-gray-600">
                      {testimonials[currentIndex].role}
                    </div>
                    <div className="text-sm text-gray-500">
                      {testimonials[currentIndex].company} • {testimonials[currentIndex].industry}
                    </div>
                  </div>
                </div>

                {/* Metrics Badge */}
                <motion.div
                  className="glass-subtle rounded-2xl px-6 py-3 border border-white/20"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-cyan-600 bg-clip-text text-transparent">
                      {testimonials[currentIndex].metrics.increase}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonials[currentIndex].metrics.metric}
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Enhanced Navigation */}
          <div className="flex items-center justify-center space-x-6 mt-8">
            <motion.button
              onClick={prev}
              className="p-3 rounded-full glass-subtle border border-white/20 hover:glass-strong transition-all duration-300 group"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ChevronLeft className="h-5 w-5 text-gray-600 group-hover:text-gray-900 transition-colors duration-200" />
            </motion.button>

            <div className="flex space-x-3">
              {testimonials.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 scale-125'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />
              ))}
            </div>

            <motion.button
              onClick={next}
              className="p-3 rounded-full glass-subtle border border-white/20 hover:glass-strong transition-all duration-300 group"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <ChevronRight className="h-5 w-5 text-gray-600 group-hover:text-gray-900 transition-colors duration-200" />
            </motion.button>
          </div>
        </motion.div>

        {/* Company Logos */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <p className="text-gray-600 mb-8">Trusted by leading companies worldwide</p>
          <div className="flex flex-wrap items-center justify-center gap-8 lg:gap-12">
            {companies.map((company, index) => (
              <motion.div
                key={index}
                className="flex items-center space-x-3 glass-subtle rounded-xl px-6 py-3 border border-white/20 hover:glass-strong transition-all duration-300"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
              >
                <span className="text-2xl">{company.logo}</span>
                <span className="font-medium text-gray-700">{company.name}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>


      </div>
    </section>
  );
}
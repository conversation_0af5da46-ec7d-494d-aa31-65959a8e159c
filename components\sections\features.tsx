'use client';

import { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import {
  Brain,
  BarChart3,
  Zap,
  Target,
  MessageSquare,
  Shield,
  ArrowRight,
  Sparkles,
  Play
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const features = [
  {
    icon: Brain,
    title: "AI Content Generation",
    description: "Generate high-converting copy, social posts, and marketing materials with advanced AI that understands your brand voice.",
    color: "from-purple-500 to-pink-500",
    stats: "10x faster content creation"
  },
  {
    icon: BarChart3,
    title: "Predictive Analytics",
    description: "Forecast campaign performance and customer behavior with machine learning-powered insights and recommendations.",
    color: "from-blue-500 to-cyan-500",
    stats: "95% accuracy rate"
  },
  {
    icon: Target,
    title: "Smart Targeting",
    description: "Identify and reach your ideal customers with AI-driven audience segmentation and behavioral analysis.",
    color: "from-green-500 to-emerald-500",
    stats: "3x better targeting"
  },
  {
    icon: Zap,
    title: "Workflow Automation",
    description: "Automate your entire marketing funnel with intelligent workflows that adapt and optimize in real-time.",
    color: "from-yellow-500 to-orange-500",
    stats: "80% time savings"
  },
  {
    icon: MessageSquare,
    title: "Brand Voice Analysis",
    description: "Maintain consistent messaging across all channels with AI-powered tone and voice optimization.",
    color: "from-indigo-500 to-purple-500",
    stats: "100% brand consistency"
  },
  {
    icon: Shield,
    title: "Privacy-First Analytics",
    description: "Get deep insights while respecting user privacy with cookieless tracking and GDPR-compliant analytics.",
    color: "from-gray-500 to-slate-500",
    stats: "GDPR compliant"
  }
];

const Features = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section id="features" className="section-padding bg-gradient-to-br from-gray-50/50 via-white to-indigo-50/30 relative overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-mesh-dark opacity-30" />
        <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" />

        {/* Floating geometric shapes */}
        <motion.div
          className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-xl"
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-16 h-16 bg-gradient-to-r from-cyan-400/10 to-blue-400/10 rounded-xl blur-lg"
          animate={{
            y: [20, -20, 20],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="relative max-width-content mx-auto container-padding" ref={ref}>
        {/* Header */}
        <motion.div 
          className="text-center max-w-4xl mx-auto mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <Badge variant="info" size="lg" className="mb-6">
            <Sparkles className="h-4 w-4 mr-2" />
            Features
          </Badge>
          <h2 className="text-display-xl text-gray-900 mb-8">
            Everything you need to scale your marketing
          </h2>
          <p className="text-body-xl text-gray-600 leading-relaxed">
            Powerful tools and insights to help you create, optimize, and measure 
            marketing campaigns that drive real business results.
          </p>
        </motion.div>

        {/* Enhanced Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              animate={isInView ? { opacity: 1, y: 0, scale: 1 } : {}}
              transition={{
                duration: 0.7,
                delay: index * 0.15,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{ y: -8 }}
            >
              <Card variant="feature" className="h-full relative overflow-hidden">
                <CardContent className="p-8 relative z-10">
                  {/* Enhanced Icon Container */}
                  <motion.div
                    className={`w-20 h-20 rounded-3xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-8 relative`}
                    whileHover={{
                      scale: 1.1,
                      rotate: 5,
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <feature.icon className="h-10 w-10 text-white relative z-10" />
                    <motion.div
                      className="absolute inset-0 rounded-3xl bg-white/20"
                      initial={{ scale: 0, opacity: 0 }}
                      whileHover={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  </motion.div>

                  {/* Enhanced Content */}
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-indigo-900 transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-pretty">
                      {feature.description}
                    </p>

                    {/* Enhanced Footer */}
                    <div className="flex items-center justify-between pt-4">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Badge variant="success" size="sm" className="glass-subtle border border-emerald-200/50">
                          {feature.stats}
                        </Badge>
                      </motion.div>
                      <motion.button
                        className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-700 group-hover:translate-x-2 transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Learn more
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </motion.button>
                    </div>
                  </div>

                  {/* Hover Gradient Overlay */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                  />
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Enhanced Bottom CTA */}
        <motion.div
          className="text-center relative"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <motion.div
            className="max-w-3xl mx-auto glass-strong rounded-3xl p-12 border border-white/20 shadow-glow relative overflow-hidden"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5 rounded-3xl" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(99,102,241,0.1),transparent_70%)]" />

            <div className="relative z-10">
              <motion.h3
                className="text-3xl font-bold text-gray-900 mb-6 text-balance"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                Ready to transform your{' '}
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
                  marketing?
                </span>
              </motion.h3>

              <motion.p
                className="text-xl text-gray-600 mb-10 leading-relaxed text-pretty"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 1.0 }}
              >
                Join thousands of teams already using ADmyBRAND to drive better results
                with AI-powered marketing intelligence.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-6 justify-center"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 1.2 }}
              >
                <Button
                  variant="gradient"
                  size="xl"
                  className="shadow-glow hover:shadow-glow-lg"
                >
                  <Sparkles className="mr-2 h-5 w-5" />
                  Start Free Trial
                </Button>
                <Button
                  variant="secondary"
                  size="xl"
                  className="glass-subtle border-white/20 hover:glass-strong"
                >
                  <Play className="mr-2 h-5 w-5" />
                  Schedule Demo
                </Button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
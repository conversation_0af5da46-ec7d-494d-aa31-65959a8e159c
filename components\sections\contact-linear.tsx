'use client';

import { motion } from 'framer-motion';
import { useState, useRef } from 'react';
import { Mail, MessageSquare, Calendar, ArrowRight, Check } from 'lucide-react';

export default function ContactLinear() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1000));

    setIsSubmitted(true);
    setIsSubmitting(false);
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <section ref={sectionRef} className="section-spacing scroll-optimized transform-gpu will-change-transform" style={{ background: 'var(--linear-bg-primary)' }}>
      <div className="content-container">
        
        {/* Section header - Linear style */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge purple mb-6"
          >
            <MessageSquare className="w-4 h-4" />
            Get in touch
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Still have questions?
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="linear-body-lg max-w-2xl mx-auto"
            style={{ 
              lineHeight: '1.5',
              letterSpacing: '-0.011em'
            }}
          >
            Our team is here to help. Get in touch and we'll respond within 24 hours.
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
          
          {/* Contact form - Linear style */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="linear-card p-8">
              <h3 className="linear-heading-lg mb-6">
                Send us a message
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block linear-body-sm mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                      Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-3 py-2 rounded-md transition-all"
                      style={{
                        background: 'var(--linear-bg-tertiary)',
                        border: '1px solid var(--linear-border)',
                        color: 'var(--linear-text-primary)'
                      }}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block linear-body-sm mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                      Email
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-3 py-2 rounded-md transition-all"
                      style={{
                        background: 'var(--linear-bg-tertiary)',
                        border: '1px solid var(--linear-border)',
                        color: 'var(--linear-text-primary)'
                      }}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block linear-body-sm mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                    Company
                  </label>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className="w-full px-3 py-2 rounded-md transition-all"
                    style={{
                      background: 'var(--linear-bg-tertiary)',
                      border: '1px solid var(--linear-border)',
                      color: 'var(--linear-text-primary)'
                    }}
                  />
                </div>
                
                <div>
                  <label className="block linear-body-sm mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={4}
                    className="w-full px-3 py-2 rounded-md transition-all resize-none"
                    style={{
                      background: 'var(--linear-bg-tertiary)',
                      border: '1px solid var(--linear-border)',
                      color: 'var(--linear-text-primary)'
                    }}
                    placeholder="Tell us about your marketing goals..."
                    required
                  />
                </div>
                
                <motion.button
                  type="submit"
                  className="w-full btn-primary-linear flex items-center justify-center gap-2 py-3"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  disabled={isSubmitted}
                >
                  {isSubmitted ? (
                    <>
                      <Check className="w-4 h-4" />
                      Message sent!
                    </>
                  ) : (
                    <>
                      Send message
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </motion.button>
              </form>
            </div>
          </motion.div>

          {/* Contact options - Linear style */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            
            {/* Quick start */}
            <div className="linear-card p-6 interactive-element"
                 style={{ cursor: 'pointer' }}>
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
                     style={{
                       background: 'var(--linear-bg-tertiary)',
                       border: '1px solid var(--linear-border)'
                     }}>
                  <ArrowRight className="w-5 h-5" style={{ color: 'var(--linear-text-primary)' }} />
                </div>

                <div>
                  <h4 className="linear-heading-sm mb-2">
                    Start free trial
                  </h4>
                  <p className="linear-body-sm mb-4">
                    Get started immediately with our 14-day free trial.
                    No credit card required.
                  </p>

                  <div className="flex gap-2">
                    <motion.button
                      className="btn-linear-primary text-sm px-4 py-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Start trial
                    </motion.button>
                    <motion.button
                      className="btn-linear-secondary text-sm px-4 py-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Contact Support
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>

            {/* Schedule demo */}
            <div className="linear-card p-6 interactive-element"
                 style={{ cursor: 'pointer' }}>
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
                     style={{
                       background: 'var(--linear-bg-tertiary)',
                       border: '1px solid var(--linear-border)'
                     }}>
                  <Calendar className="w-5 h-5" style={{ color: 'var(--linear-text-primary)' }} />
                </div>

                <div>
                  <h4 className="linear-heading-sm mb-2">
                    Schedule a demo
                  </h4>
                  <p className="linear-body-sm mb-4">
                    Book a 30-minute demo with our team to see ADmyBRAND 
                    in action.
                  </p>
                  
                  <div className="flex gap-2">
                    <motion.button
                      className="btn-linear-secondary text-sm px-4 py-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Book demo
                    </motion.button>
                    <motion.button
                      className="btn-linear-secondary text-sm px-4 py-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Schedule Call
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>

            {/* Email support */}
            <div className="linear-card p-6 interactive-element"
                 style={{ cursor: 'pointer' }}>
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
                     style={{
                       background: 'var(--linear-bg-tertiary)',
                       border: '1px solid var(--linear-border)'
                     }}>
                  <Mail className="w-5 h-5" style={{ color: 'var(--linear-text-primary)' }} />
                </div>

                <div>
                  <h4 className="linear-heading-sm mb-2">
                    Email support
                  </h4>
                  <p className="linear-body-sm mb-4">
                    Get help from our support team. We typically respond
                    within 2 hours.
                  </p>

                  <div className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>
                    <EMAIL>
                  </div>
                </div>
              </div>
            </div>

            {/* Response time */}
            <div className="linear-card p-6">
              <div className="text-center">
                <div className="linear-heading-lg mb-1">
                  &lt; 2 hours
                </div>
                <div className="linear-body-sm">
                  Average response time
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

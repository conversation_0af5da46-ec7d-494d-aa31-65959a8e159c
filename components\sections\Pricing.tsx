'use client';

import { useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { Check, ArrowRight, Star, Zap, Crown, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const plans = [
  {
    name: "Starter",
    price: "$29",
    period: "per month",
    description: "Perfect for small teams getting started",
    icon: Zap,
    color: "from-emerald-500 to-cyan-500",
    features: [
      "Up to 5 team members",
      "10,000 AI-generated words/month",
      "Basic analytics dashboard",
      "Email support",
      "Campaign templates",
      "Social media scheduling"
    ],
    cta: "Start free trial",
    popular: false,
    savings: null
  },
  {
    name: "Professional",
    price: "$99",
    period: "per month",
    description: "For growing teams that need more power",
    icon: Star,
    color: "from-indigo-500 to-purple-500",
    features: [
      "Up to 25 team members",
      "100,000 AI-generated words/month",
      "Advanced predictive analytics",
      "Priority support + live chat",
      "Custom workflows",
      "A/B testing suite",
      "Brand voice training",
      "API access"
    ],
    cta: "Start free trial",
    popular: true,
    savings: "Save 20%"
  },
  {
    name: "Enterprise",
    price: "Custom",
    period: "pricing",
    description: "For large organizations with advanced needs",
    icon: Crown,
    color: "from-purple-500 to-pink-500",
    features: [
      "Unlimited team members",
      "Unlimited AI generation",
      "Custom analytics + reports",
      "Dedicated account manager",
      "Custom AI model training",
      "White-label solutions",
      "Advanced integrations",
      "SLA guarantee"
    ],
    cta: "Contact sales",
    popular: false,
    savings: "Custom pricing"
  }
];

export default function Pricing() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  return (
    <section id="pricing" className="section-padding bg-gradient-to-br from-gray-50/50 via-white to-purple-50/30 relative overflow-hidden" ref={ref}>
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.02)_1px,transparent_1px)] bg-[size:48px_48px]" />

        {/* Floating elements */}
        <motion.div
          className="absolute top-20 left-10 w-24 h-24 bg-gradient-to-r from-indigo-400/10 to-purple-400/10 rounded-full blur-xl"
          animate={{
            y: [-30, 30, -30],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-20 h-20 bg-gradient-to-r from-cyan-400/10 to-blue-400/10 rounded-xl blur-lg"
          animate={{
            y: [30, -30, 30],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="max-width-content mx-auto container-padding relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="inline-flex items-center space-x-3 glass-subtle rounded-full px-6 py-3 mb-8 border border-white/20"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Sparkles className="h-4 w-4 text-indigo-600" />
            <span className="text-sm font-medium text-gray-700">Pricing</span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 text-balance"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Simple, transparent{' '}
            <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
              pricing
            </span>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed text-pretty"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            Start free, scale as you grow. All plans include our core AI features
            with 14-day free trial and no setup fees.
          </motion.p>

          {/* Billing Toggle */}
          <motion.div
            className="flex items-center justify-center mt-10"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="glass-strong rounded-2xl p-2 border border-white/20">
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setBillingPeriod('monthly')}
                  className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                    billingPeriod === 'monthly'
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-glow'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingPeriod('yearly')}
                  className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 relative ${
                    billingPeriod === 'yearly'
                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-glow'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Yearly
                  <Badge
                    variant="success"
                    size="sm"
                    className="absolute -top-2 -right-2 text-xs"
                  >
                    Save 20%
                  </Badge>
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Enhanced Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              animate={isInView ? { opacity: 1, y: 0, scale: 1 } : {}}
              transition={{
                duration: 0.7,
                delay: index * 0.2,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{ y: -8, scale: 1.02 }}
              className={`relative group cursor-pointer ${
                plan.popular ? 'lg:scale-105' : ''
              }`}
            >
              <div className={`relative glass-strong rounded-3xl p-8 border transition-all duration-500 overflow-hidden ${
                plan.popular
                  ? 'border-indigo-200/50 shadow-glow-lg'
                  : 'border-white/20 shadow-colored hover:shadow-glow'
              }`}>

                {/* Popular Badge */}
                {plan.popular && (
                  <motion.div
                    className="absolute -top-4 left-1/2 transform -translate-x-1/2"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <div className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-6 py-2 rounded-full text-sm font-medium shadow-glow">
                      <Star className="inline h-4 w-4 mr-1" />
                      Most Popular
                    </div>
                  </motion.div>
                )}

                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${plan.color} opacity-5 rounded-3xl group-hover:opacity-10 transition-opacity duration-500`} />

                {/* Icon */}
                <motion.div
                  className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${plan.color} flex items-center justify-center mb-6 relative z-10`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <plan.icon className="h-8 w-8 text-white" />
                </motion.div>

                <div className="relative z-10">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600 mb-6 text-pretty">{plan.description}</p>

                    {/* Price */}
                    <div className="mb-6">
                      <motion.span
                        className="text-5xl font-bold text-gray-900"
                        initial={{ scale: 0.8 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: index * 0.1 + 1 }}
                      >
                        {billingPeriod === 'yearly' && plan.price !== 'Custom'
                          ? `$${Math.round(parseInt(plan.price.replace('$', '')) * 0.8)}`
                          : plan.price
                        }
                      </motion.span>
                      {plan.price !== "Custom" && (
                        <span className="text-gray-600 ml-1">
                          /{billingPeriod === 'yearly' ? 'year' : plan.period}
                        </span>
                      )}
                      {plan.savings && billingPeriod === 'yearly' && (
                        <div className="mt-2">
                          <Badge variant="success" size="sm">
                            {plan.savings}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Features */}
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <motion.li
                        key={featureIndex}
                        className="flex items-start space-x-3"
                        initial={{ opacity: 0, x: -20 }}
                        animate={isInView ? { opacity: 1, x: 0 } : {}}
                        transition={{ duration: 0.5, delay: index * 0.1 + featureIndex * 0.1 + 1.2 }}
                      >
                        <div className="w-5 h-5 rounded-full bg-gradient-to-r from-emerald-500 to-cyan-500 flex items-center justify-center mt-0.5 flex-shrink-0">
                          <Check className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-gray-700 leading-relaxed">{feature}</span>
                      </motion.li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <Button
                    variant={plan.popular ? "gradient" : "secondary"}
                    size="lg"
                    className={`w-full group ${
                      plan.popular
                        ? 'shadow-glow hover:shadow-glow-lg'
                        : 'glass-subtle border-white/20 hover:glass-strong'
                    }`}
                  >
                    {plan.cta}
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Info */}
        <div className="text-center mt-12">
          <p className="text-body text-gray-600 mb-4">
            All plans include 14-day free trial • No setup fees • Cancel anytime
          </p>
          <p className="text-caption text-gray-500">
            Need a custom solution? 
            <button className="text-gray-900 font-medium ml-1 hover:underline">
              Contact our sales team
            </button>
          </p>
        </div>
      </div>
    </section>
  );
}
import Head from 'next/head';

interface MetaTagsProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: 'summary' | 'summary_large_image';
  canonicalUrl?: string;
  noIndex?: boolean;
}

export function MetaTags({
  title = 'ADmyBRAND AI Suite - Transform Your Marketing with AI Intelligence',
  description = 'Revolutionize your marketing strategy with our comprehensive AI-powered suite. Advanced analytics, automated campaigns, and intelligent insights to drive growth.',
  keywords = 'AI marketing, marketing automation, digital marketing, AI analytics, marketing intelligence, automated campaigns, marketing optimization',
  ogImage = '/og-image.jpg',
  ogUrl = 'https://admybrand.com',
  twitterCard = 'summary_large_image',
  canonicalUrl,
  noIndex = false,
}: MetaTagsProps) {
  const fullTitle = title.includes('ADmyBRAND') ? title : `${title} | ADmyBRAND AI Suite`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="ADmyBRAND" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#6366f1" />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? 'noindex,nofollow' : 'index,follow'} />
      
      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={ogUrl} />
      <meta property="og:site_name" content="ADmyBRAND AI Suite" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:site" content="@admybrand" />
      <meta name="twitter:creator" content="@admybrand" />
      
      {/* Favicon */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "ADmyBRAND AI Suite",
            "description": description,
            "url": ogUrl,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD",
              "description": "Free 14-day trial available"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "ratingCount": "1247",
              "bestRating": "5",
              "worstRating": "1"
            },
            "provider": {
              "@type": "Organization",
              "name": "ADmyBRAND",
              "url": ogUrl
            }
          })
        }}
      />
    </Head>
  );
}

// Page-specific meta components
export function HomePageMeta() {
  return (
    <MetaTags
      title="ADmyBRAND AI Suite - Transform Your Marketing with AI Intelligence"
      description="Revolutionize your marketing strategy with our comprehensive AI-powered suite. Advanced analytics, automated campaigns, and intelligent insights to drive growth."
      keywords="AI marketing, marketing automation, digital marketing, AI analytics, marketing intelligence, automated campaigns, marketing optimization, lead generation, customer insights"
      canonicalUrl="https://admybrand.com"
    />
  );
}

export function FeaturesPageMeta() {
  return (
    <MetaTags
      title="Features - AI-Powered Marketing Tools"
      description="Discover powerful AI marketing features including automated campaigns, advanced analytics, customer insights, and intelligent optimization tools."
      keywords="AI marketing features, automated campaigns, marketing analytics, customer insights, marketing optimization, AI tools"
      canonicalUrl="https://admybrand.com/features"
    />
  );
}

export function PricingPageMeta() {
  return (
    <MetaTags
      title="Pricing - Affordable AI Marketing Solutions"
      description="Choose the perfect AI marketing plan for your business. Flexible pricing with free trial. Start transforming your marketing today."
      keywords="AI marketing pricing, marketing automation pricing, affordable marketing tools, marketing software cost"
      canonicalUrl="https://admybrand.com/pricing"
    />
  );
}

export function ContactPageMeta() {
  return (
    <MetaTags
      title="Contact Us - Get Started with AI Marketing"
      description="Ready to transform your marketing with AI? Contact our team for personalized consultation and start your free trial today."
      keywords="contact AI marketing, marketing consultation, AI marketing support, get started"
      canonicalUrl="https://admybrand.com/contact"
    />
  );
}

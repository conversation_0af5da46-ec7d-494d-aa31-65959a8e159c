'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import {
  Twitter,
  Linkedin,
  Github,
  Youtube,
  ArrowRight,
  Mail,
  MapPin,
  Phone,
  Send,
  Heart,
  Sparkles,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

const footerLinks = {
  product: [
    { name: "Features", href: "#features", description: "Explore our AI tools" },
    { name: "Pricing", href: "#pricing", description: "Simple, transparent pricing" },
    { name: "API", href: "#", description: "Developer resources" },
    { name: "Integrations", href: "#", description: "Connect your tools" },
    { name: "Changelog", href: "#", description: "Latest updates" }
  ],
  company: [
    { name: "About", href: "#", description: "Our story and mission" },
    { name: "Blog", href: "#", description: "Marketing insights" },
    { name: "Careers", href: "#", description: "Join our team" },
    { name: "Press", href: "#", description: "Media resources" },
    { name: "Partners", href: "#", description: "Partnership program" }
  ],
  resources: [
    { name: "Documentation", href: "#", description: "Complete guides" },
    { name: "Help Center", href: "#", description: "Get support" },
    { name: "Community", href: "#", description: "Connect with users" },
    { name: "Templates", href: "#", description: "Ready-to-use templates" },
    { name: "Status", href: "#", description: "System status" }
  ],
  legal: [
    { name: "Privacy Policy", href: "#", description: "How we protect your data" },
    { name: "Terms of Service", href: "#", description: "Usage terms" },
    { name: "Security", href: "#", description: "Security practices" },
    { name: "Cookie Policy", href: "#", description: "Cookie usage" }
  ]
};

const socialLinks = [
  {
    name: "Twitter",
    icon: Twitter,
    href: "#",
    color: "hover:text-blue-400",
    bgColor: "hover:bg-blue-500/10"
  },
  {
    name: "LinkedIn",
    icon: Linkedin,
    href: "#",
    color: "hover:text-blue-600",
    bgColor: "hover:bg-blue-600/10"
  },
  {
    name: "GitHub",
    icon: Github,
    href: "#",
    color: "hover:text-gray-900",
    bgColor: "hover:bg-gray-900/10"
  },
  {
    name: "YouTube",
    icon: Youtube,
    href: "#",
    color: "hover:text-red-500",
    bgColor: "hover:bg-red-500/10"
  }
];

const contactInfo = [
  {
    icon: Mail,
    label: "Email",
    value: "<EMAIL>"
  },
  {
    icon: Phone,
    label: "Phone",
    value: "+****************"
  },
  {
    icon: MapPin,
    label: "Address",
    value: "San Francisco, CA"
  }
];

export default function Footer() {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubscribing(true);

    // Simulate newsletter subscription
    await new Promise(resolve => setTimeout(resolve, 1500));

    toast.success('Successfully subscribed to our newsletter!');
    setEmail('');
    setIsSubscribing(false);
  };

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden" ref={ref}>
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.05)_1px,transparent_1px)] bg-[size:32px_32px]" />

        {/* Floating elements */}
        <motion.div
          className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 rounded-full blur-3xl"
          animate={{
            y: [-20, 20, -20],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-xl blur-2xl"
          animate={{
            y: [20, -20, 20],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="relative max-width-content mx-auto container-padding py-20">
        {/* Enhanced Newsletter */}
        <motion.div
          className="glass-strong rounded-3xl p-8 lg:p-12 mb-20 border border-white/10 shadow-glow"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <motion.div
                  className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-indigo-500 flex items-center justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <Sparkles className="h-6 w-6 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">
                    Stay in the loop
                  </h3>
                  <p className="text-gray-300 text-sm">Join 10,000+ marketers</p>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed text-pretty">
                Get the latest product updates, marketing insights, and exclusive tips
                delivered straight to your inbox. No spam, unsubscribe anytime.
              </p>
            </motion.div>

            <motion.form
              onSubmit={handleNewsletterSubmit}
              className="space-y-4"
              initial={{ opacity: 0, x: 30 }}
              animate={isInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-6 py-4 glass-subtle rounded-2xl border border-white/20 focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-300 transition-all duration-300 text-white placeholder-gray-400 pr-32"
                  required
                />
                <Button
                  type="submit"
                  variant="gradient"
                  disabled={isSubscribing}
                  className="absolute right-2 top-2 bottom-2 px-6 shadow-glow"
                >
                  {isSubscribing ? (
                    <motion.div
                      className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span>Free marketing insights every week</span>
              </div>
            </motion.form>
          </div>
        </motion.div>

        {/* Enhanced Links Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-6 gap-8 lg:gap-12 mb-16">
          {/* Enhanced Brand */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <motion.div
              className="flex items-center space-x-3 mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-glow">
                <span className="text-white font-bold text-lg">A</span>
              </div>
              <span className="font-bold text-2xl text-white">
                ADmyBRAND
              </span>
            </motion.div>
            <p className="text-gray-300 mb-8 max-w-sm leading-relaxed text-pretty">
              AI-powered marketing intelligence that helps teams create,
              optimize, and scale their campaigns with unprecedented precision.
            </p>

            {/* Contact Info */}
            <div className="space-y-3 mb-8">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  className="flex items-center space-x-3 text-gray-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={isInView ? { opacity: 1, x: 0 } : {}}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                >
                  <info.icon className="h-4 w-4 text-purple-400" />
                  <span className="text-sm">{info.value}</span>
                </motion.div>
              ))}
            </div>

            {/* Social Links */}
            <div className="flex space-x-3">
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <motion.a
                    key={social.name}
                    href={social.href}
                    className={`w-12 h-12 glass-subtle rounded-xl flex items-center justify-center border border-white/10 text-gray-400 transition-all duration-300 ${social.color} ${social.bgColor}`}
                    aria-label={social.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : {}}
                    transition={{ duration: 0.6, delay: 1.0 + index * 0.1 }}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon className="h-5 w-5" />
                  </motion.a>
                );
              })}
            </div>
          </motion.div>
          
          {/* Enhanced Navigation Links */}
          {Object.entries(footerLinks).map(([category, links], categoryIndex) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.8 + categoryIndex * 0.1 }}
            >
              <h4 className="font-semibold text-white mb-6 text-lg capitalize">
                {category}
              </h4>
              <ul className="space-y-4">
                {links.map((link, linkIndex) => (
                  <motion.li
                    key={link.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={isInView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.6, delay: 1.0 + categoryIndex * 0.1 + linkIndex * 0.05 }}
                  >
                    <motion.a
                      href={link.href}
                      className="group flex items-start space-x-2 text-gray-300 hover:text-white transition-all duration-300"
                      whileHover={{ x: 5 }}
                    >
                      <span className="text-sm font-medium group-hover:text-purple-400 transition-colors duration-200">
                        {link.name}
                      </span>
                    </motion.a>
                    <p className="text-xs text-gray-500 mt-1 ml-0 group-hover:text-gray-400 transition-colors duration-200">
                      {link.description}
                    </p>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Enhanced Bottom Section */}
        <motion.div
          className="flex flex-col lg:flex-row justify-between items-center pt-12 border-t border-white/10"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.4 }}
        >
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 mb-6 lg:mb-0">
            <div className="text-gray-400 text-sm">
              © 2025 ADmyBRAND. All rights reserved.
            </div>
            <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full" />
            <div className="flex items-center space-x-2 text-gray-400 text-sm">
              <span>Made with</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <Heart className="h-4 w-4 text-red-500 fill-current" />
              </motion.div>
              <span>in San Francisco</span>
            </div>
          </div>

          <div className="flex items-center space-x-6 text-sm text-gray-400">
            <motion.a
              href="#"
              className="hover:text-white transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
            >
              Privacy Policy
            </motion.a>
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
            <motion.a
              href="#"
              className="hover:text-white transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
            >
              Terms of Service
            </motion.a>
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
            <motion.a
              href="#"
              className="hover:text-white transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
            >
              Cookie Settings
            </motion.a>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
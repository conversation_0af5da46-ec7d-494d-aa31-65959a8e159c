<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>OG Image Generator</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #canvas { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h2>OG Image Generator</h2>
    <canvas id="canvas" width="1200" height="630"></canvas>
    <br><br>
    <button onclick="downloadImage()">Download as JPG</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Background
        ctx.fillStyle = '#0D1117';
        ctx.fillRect(0, 0, 1200, 630);
        
        // Gradient overlay
        const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
        gradient.addColorStop(0, '#1a1a1a');
        gradient.addColorStop(1, '#0D1117');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 1200, 630);
        
        // Main title
        ctx.fillStyle = '#F0F6FC';
        ctx.font = 'bold 64px Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('ADmyBRAND AI Suite', 600, 250);
        
        // Subtitle
        ctx.fillStyle = '#8B949E';
        ctx.font = '32px Arial, sans-serif';
        ctx.fillText('Marketing Intelligence That Works', 600, 320);
        
        // Description
        ctx.fillStyle = '#6E7681';
        ctx.font = '24px Arial, sans-serif';
        ctx.fillText('Transform your marketing with AI-powered insights', 600, 380);
        
        // Decorative elements
        ctx.fillStyle = 'rgba(88, 166, 255, 0.6)';
        ctx.beginPath();
        ctx.arc(200, 150, 3, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = 'rgba(88, 166, 255, 0.4)';
        ctx.beginPath();
        ctx.arc(1000, 480, 4, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = 'rgba(88, 166, 255, 0.8)';
        ctx.beginPath();
        ctx.arc(150, 500, 2, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = 'rgba(88, 166, 255, 0.5)';
        ctx.beginPath();
        ctx.arc(1050, 120, 3, 0, 2 * Math.PI);
        ctx.fill();
        
        // Brand accent line
        ctx.fillStyle = 'rgba(88, 166, 255, 0.7)';
        ctx.fillRect(400, 420, 400, 2);
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'og-image.jpg';
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }
    </script>
</body>
</html>

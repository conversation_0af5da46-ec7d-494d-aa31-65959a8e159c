// Simple script to create placeholder icon files
const fs = require('fs');

// Create minimal PNG files (1x1 transparent pixel)
const minimalPngBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==';

// Convert base64 to buffer
const buffer = Buffer.from(minimalPngBase64, 'base64');

// Create icon files
fs.writeFileSync('icon-192x192.png', buffer);
fs.writeFileSync('icon-512x512.png', buffer);
fs.writeFileSync('apple-touch-icon.png', buffer);
fs.writeFileSync('favicon.ico', buffer);

console.log('Created placeholder icon files:');
console.log('- icon-192x192.png');
console.log('- icon-512x512.png'); 
console.log('- apple-touch-icon.png');
console.log('- favicon.ico');

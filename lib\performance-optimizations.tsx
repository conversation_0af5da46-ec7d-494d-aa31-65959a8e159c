'use client';

import React, { useEffect, useCallback, useMemo, useState, useRef, lazy, Suspense } from 'react';

// Debounce hook for performance optimization
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle hook for scroll events
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [ref, setRef] = useState<Element | null>(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [ref, options]);

  return [setRef, isIntersecting] as const;
};

// Preload images for better performance
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

// Preload multiple images
export const preloadImages = async (srcs: string[]): Promise<void> => {
  try {
    await Promise.all(srcs.map(preloadImage));
  } catch (error) {
    console.warn('Failed to preload some images:', error);
  }
};

// Lazy load component wrapper
export const LazyLoadWrapper = ({ 
  children, 
  fallback = null,
  rootMargin = '50px'
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  rootMargin?: string;
}) => {
  const [setRef, isIntersecting] = useIntersectionObserver({
    rootMargin,
    threshold: 0.1,
  });

  return (
    <div ref={setRef}>
      {isIntersecting ? children : fallback}
    </div>
  );
};

// Optimize animations for performance
export const optimizeAnimation = {
  // Use transform instead of changing layout properties
  transform: {
    willChange: 'transform',
    backfaceVisibility: 'hidden' as const,
    perspective: 1000,
  },
  
  // Optimize opacity animations
  opacity: {
    willChange: 'opacity',
  },
  
  // GPU acceleration
  gpu: {
    transform: 'translateZ(0)',
    willChange: 'transform',
  },
};

// Performance monitoring
export const performanceMonitor = {
  // Measure component render time
  measureRender: (componentName: string) => {
    const start = performance.now();
    return () => {
      const end = performance.now();
      console.log(`${componentName} render time: ${end - start}ms`);
    };
  },

  // Measure function execution time
  measureFunction: <T extends (...args: any[]) => any>(
    fn: T,
    name: string
  ): T => {
    return ((...args: any[]) => {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      console.log(`${name} execution time: ${end - start}ms`);
      return result;
    }) as T;
  },

  // Monitor memory usage
  monitorMemory: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('Memory usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB',
      });
    }
  },
};

// Optimize scroll performance
export const optimizeScroll = {
  // Passive scroll listener
  addPassiveListener: (
    element: Element | Window,
    event: string,
    handler: EventListener
  ) => {
    element.addEventListener(event, handler, { passive: true });
    return () => element.removeEventListener(event, handler);
  },

  // Throttled scroll handler
  createThrottledScrollHandler: (
    handler: () => void,
    delay: number = 16
  ) => {
    let ticking = false;
    
    return () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handler();
          ticking = false;
        });
        ticking = true;
      }
    };
  },
};

// Image optimization utilities
export const imageOptimization = {
  // Generate responsive image sources
  generateSrcSet: (
    baseSrc: string,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
  ) => {
    return sizes
      .map(size => `${baseSrc}?w=${size} ${size}w`)
      .join(', ');
  },

  // Generate sizes attribute
  generateSizes: (breakpoints: { [key: string]: string }) => {
    return Object.entries(breakpoints)
      .map(([breakpoint, size]) => `(max-width: ${breakpoint}) ${size}`)
      .join(', ');
  },

  // Lazy loading image component
  LazyImage: ({ 
    src, 
    alt, 
    className = '',
    ...props 
  }: {
    src: string;
    alt: string;
    className?: string;
    [key: string]: any;
  }) => {
    const [setRef, isIntersecting] = useIntersectionObserver({
      rootMargin: '50px',
    });
    const [loaded, setLoaded] = useState(false);

    return (
      <div ref={setRef} className={`relative ${className}`}>
        {isIntersecting && (
          <img
            src={src}
            alt={alt}
            onLoad={() => setLoaded(true)}
            className={`transition-opacity duration-300 ${
              loaded ? 'opacity-100' : 'opacity-0'
            }`}
            {...props}
          />
        )}
        {!loaded && isIntersecting && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
        )}
      </div>
    );
  },
};

// Bundle size optimization
export const bundleOptimization = {
  // Dynamic import wrapper
  dynamicImport: <T>(importFn: () => Promise<T>) => {
    return lazy(() => importFn());
  },

  // Code splitting utility
  splitComponent: <T extends React.ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback: React.ComponentType = () => <div>Loading...</div>
  ) => {
    const Component = lazy(importFn);
    
    return (props: React.ComponentProps<T>) => (
      <Suspense fallback={<fallback />}>
        <Component {...props} />
      </Suspense>
    );
  },
};



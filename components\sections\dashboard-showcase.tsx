'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { 
  MapPin, 
  BarChart3, 
  TrendingUp, 
  Radio, 
  Tv, 
  Newspaper,
  Eye,
  Target,
  Zap,
  Globe,
  ArrowRight,
  Play,
  Pause
} from 'lucide-react';

const dashboardData = [
  {
    id: 'outdoor',
    title: 'Outdoor Media Map',
    subtitle: '2,847 spots',
    locations: ['Mumbai', 'Delhi', '+15'],
    stats: {
      available: '1,247',
      avgCost: '₹45K'
    },
    description: 'Interactive map-based booking of hoardings, billboards, and outdoor spaces with real-time availability and pricing.',
    features: ['Interactive Location Maps', 'Real-time Availability', 'Dynamic Pricing', 'Coverage Analytics'],
    color: 'from-blue-500 to-cyan-500',
    icon: MapPin
  },
  {
    id: 'digital',
    title: 'Digital Campaign Hub',
    subtitle: '47 Live Campaigns',
    stats: {
      spent: '₹2.4L',
      ctr: '4.2%',
      cpc: '₹8.50',
      roas: '5.8x'
    },
    description: 'Advanced digital advertising dashboard with real-time analytics, performance tracking, and AI-powered optimization.',
    features: ['Live Performance Tracking', 'AI Optimization', 'Multi-platform Management', 'ROI Analytics'],
    color: 'from-purple-500 to-pink-500',
    icon: BarChart3
  },
  {
    id: 'analytics',
    title: 'Unified Analytics Platform',
    subtitle: 'Real-time Performance',
    stats: {
      outdoor: '85%',
      digital: '72%',
      print: '58%',
      reach: '2.4M',
      roi: '4.2x'
    },
    description: 'Comprehensive analytics dashboard combining all media channels with advanced reporting and predictive insights.',
    features: ['Cross-channel Analytics', 'Predictive Insights', 'Custom Dashboards', 'Real-time Reporting'],
    color: 'from-green-500 to-emerald-500',
    icon: TrendingUp
  },
  {
    id: 'traditional',
    title: 'Traditional Media Planner',
    subtitle: 'Media Planning Hub',
    stats: {
      radio: '127 stations',
      tv: 'Scheduled',
      print: 'Pending'
    },
    description: 'Intelligent planning and booking system for radio, TV, and print media with audience reach optimization.',
    features: ['Audience Optimization', 'Media Planning Tools', 'Booking Automation', 'Performance Tracking'],
    color: 'from-orange-500 to-red-500',
    icon: Radio
  }
];

export default function DashboardShowcase() {
  const [activeTab, setActiveTab] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // Auto-slide functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % dashboardData.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const activeData = dashboardData[activeTab];

  return (
    <section 
      ref={sectionRef} 
      className="section-spacing scroll-optimized transform-gpu will-change-transform" 
      style={{ background: 'var(--linear-bg-primary)' }}
    >
      <div className="content-container">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge blue mb-6"
          >
            <Zap className="w-4 h-4" />
            Platform Overview
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Complete Media Management Platform
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="linear-text-lg max-w-3xl mx-auto"
            style={{ color: 'var(--linear-text-secondary)' }}
          >
            Access India's largest inventory of 10M+ advertising options across all media channels with real-time analytics and AI-powered optimization.
          </motion.p>
        </div>

        {/* Main Dashboard Interface */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Side - Dashboard Preview */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Mock Dashboard Container */}
            <div 
              className="linear-card rounded-2xl p-8 relative overflow-hidden"
              style={{ background: 'var(--linear-bg-secondary)', border: '1px solid var(--linear-border)' }}
            >
              {/* Dashboard Header */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                    <activeData.icon className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold" style={{ color: 'var(--linear-text-primary)' }}>
                      {activeData.title}
                    </h3>
                    <p className="text-sm" style={{ color: 'var(--linear-text-muted)' }}>
                      {activeData.subtitle}
                    </p>
                  </div>
                </div>
                
                {/* Auto-play Controls */}
                <button
                  onClick={() => setIsAutoPlaying(!isAutoPlaying)}
                  className="p-2 rounded-lg hover:bg-white/5 transition-colors"
                  style={{ color: 'var(--linear-text-secondary)' }}
                >
                  {isAutoPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </button>
              </div>

              {/* Animated Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-6"
                >
                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(activeData.stats).map(([key, value], index) => (
                      <motion.div
                        key={key}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="linear-card p-4 rounded-lg"
                        style={{ background: 'var(--linear-bg-tertiary)' }}
                      >
                        <div className="text-sm capitalize" style={{ color: 'var(--linear-text-muted)' }}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                        <div className="text-lg font-semibold" style={{ color: 'var(--linear-text-primary)' }}>
                          {value}
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Progress Bars */}
                  <div className="space-y-3">
                    {activeData.features.slice(0, 3).map((feature, index) => (
                      <div key={feature} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span style={{ color: 'var(--linear-text-secondary)' }}>{feature}</span>
                          <span style={{ color: 'var(--linear-text-muted)' }}>{85 - index * 10}%</span>
                        </div>
                        <div className="h-2 bg-white/5 rounded-full overflow-hidden">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${85 - index * 10}%` }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                            className={`h-full bg-gradient-to-r ${activeData.color} rounded-full`}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Floating Elements */}
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 2, 0]
              }}
              transition={{ 
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl backdrop-blur-sm border border-white/10"
            />
          </motion.div>

          {/* Right Side - Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Tab Navigation */}
            <div className="flex flex-wrap gap-2">
              {dashboardData.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => {
                    setActiveTab(index);
                    setIsAutoPlaying(false);
                  }}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                    activeTab === index 
                      ? 'bg-white/10 border border-white/20' 
                      : 'hover:bg-white/5'
                  }`}
                  style={{ 
                    color: activeTab === index 
                      ? 'var(--linear-text-primary)' 
                      : 'var(--linear-text-secondary)' 
                  }}
                >
                  <item.icon className="w-4 h-4 inline mr-2" />
                  {item.title.split(' ')[0]}
                </button>
              ))}
            </div>

            {/* Active Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--linear-text-primary)' }}>
                    {activeData.title}
                  </h3>
                  <p className="text-lg leading-relaxed" style={{ color: 'var(--linear-text-secondary)' }}>
                    {activeData.description}
                  </p>
                </div>

                {/* Features List */}
                <div className="grid grid-cols-2 gap-3">
                  {activeData.features.map((feature, index) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-center space-x-2"
                    >
                      <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${activeData.color}`} />
                      <span className="text-sm" style={{ color: 'var(--linear-text-secondary)' }}>
                        {feature}
                      </span>
                    </motion.div>
                  ))}
                </div>

                {/* CTA */}
                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="btn-primary-linear flex items-center gap-2 group"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Explore {activeData.title.split(' ')[0]}
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </motion.button>
              </motion.div>
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Bottom Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
        >
          {[
            { label: '10M+ Ad Options', value: 'Massive Inventory' },
            { label: 'Real-time Availability', value: 'Live Updates' },
            { label: 'White Label Solutions', value: 'Custom Portals' },
            { label: 'AI Optimization', value: 'Smart Campaigns' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-2xl font-bold mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                {stat.label}
              </div>
              <div className="text-sm" style={{ color: 'var(--linear-text-muted)' }}>
                {stat.value}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}

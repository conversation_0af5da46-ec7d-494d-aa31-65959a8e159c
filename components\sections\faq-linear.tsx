'use client';

import { motion } from 'framer-motion';
import { useState, useRef } from 'react';
import { Plus, Minus, HelpCircle } from 'lucide-react';

const faqs = [
  {
    question: 'What types of advertising can I book through ADmyBRAND?',
    answer: 'You can book outdoor media (hoardings, billboards), digital advertising (mobile, social media), traditional media (radio, TV, newspapers), transport advertising (buses, metros), and venue-based advertising (airports, malls, stations).'
  },
  {
    question: 'How many advertising options are available?',
    answer: 'We have over 10 million advertising options across India, covering all major cities and towns. Our inventory includes outdoor, digital, print, radio, TV, and mobile advertising spaces.'
  },
  {
    question: 'Is it free to use ADmyBRAND?',
    answer: 'Yes, searching and booking advertising spaces is completely free for advertisers. We earn through small commissions from successful bookings, so there are no upfront costs or hidden fees.'
  },
  {
    question: 'How do I track my campaign performance?',
    answer: 'We provide real-time tracking and detailed performance reports for all campaigns. You can monitor reach, impressions, engagement, and ROI through our analytics dashboard and mobile app.'
  },
  {
    question: 'Do you offer white-label solutions?',
    answer: 'Yes, we provide white-label portals for advertising agencies and media owners. These include custom branding, client management tools, inventory management, and revenue sharing options.'
  },
  {
    question: 'What support do you provide?',
    answer: 'We offer comprehensive support including campaign planning assistance, dedicated account managers for agencies, technical support, and training for our platform and mobile app.'
  }
];

export default function FAQLinear() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section ref={sectionRef} className="section-spacing scroll-optimized transform-gpu will-change-transform" style={{ background: 'var(--linear-bg-primary)' }}>
      <div className="content-container">
        
        {/* Section header - Linear style */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge purple mb-6"
          >
            <HelpCircle className="w-4 h-4" />
            FAQ
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-semibold text-gray-900 mb-6"
            style={{ 
              lineHeight: '1.1',
              letterSpacing: '-0.02em'
            }}
          >
            Frequently asked
            <br />
            <span className="text-gray-700">questions</span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg text-gray-500 max-w-2xl mx-auto"
            style={{ 
              lineHeight: '1.5',
              letterSpacing: '-0.011em'
            }}
          >
            Everything you need to know about ADmyBRAND. 
            Can't find what you're looking for? Contact our support team.
          </motion.p>
        </div>

        {/* FAQ items - Linear style */}
        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="mb-4"
            >
              <motion.button
                className="w-full linear-card rounded-lg p-6 text-left interactive-element"
                onClick={() => toggleFAQ(index)}
                whileHover={{ 
                  borderColor: 'rgb(212 212 212)',
                  transition: { duration: 0.15 }
                }}
                whileTap={{ scale: 0.995 }}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium pr-4" style={{ color: 'var(--linear-text-primary)' }}>
                    {faq.question}
                  </h3>
                  
                  <motion.div
                    animate={{ rotate: openIndex === index ? 45 : 0 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="flex-shrink-0"
                  >
                    <Plus className="w-5 h-5" style={{ color: 'var(--linear-text-secondary)' }} />
                  </motion.div>
                </div>
                
                <motion.div
                  initial={false}
                  animate={{
                    height: openIndex === index ? 'auto' : 0,
                    opacity: openIndex === index ? 1 : 0
                  }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="overflow-hidden"
                >
                  <div className="pt-4 leading-relaxed" style={{ color: 'var(--linear-text-secondary)' }}>
                    {faq.answer}
                  </div>
                </motion.div>
              </motion.button>
            </motion.div>
          ))}
        </div>

        {/* Contact CTA - Linear style */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="linear-card p-8 max-w-lg mx-auto" style={{ background: 'var(--linear-bg-secondary)', border: '1px solid var(--linear-border)' }}>
            <h3 className="text-xl font-semibold mb-3" style={{ color: 'var(--linear-text-primary)' }}>
              Still have questions?
            </h3>
            <p className="mb-6" style={{ color: 'var(--linear-text-secondary)' }}>
              Our team is here to help. Get in touch and we'll respond within 24 hours.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <motion.button 
                className="btn-primary-linear px-6 py-3 text-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                Contact Support
              </motion.button>
              
              <motion.button 
                className="btn-secondary-linear px-6 py-3 text-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                Schedule Call
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

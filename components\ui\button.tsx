'use client';

import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
  loading?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', children, loading, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus-ring disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden';
    
    const variants = {
      primary: 'bg-black text-white hover:bg-gray-800 shadow-medium hover:shadow-large',
      secondary: 'bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 shadow-soft hover:shadow-medium',
      ghost: 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
      outline: 'border border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50',
      gradient: 'interactive-button text-white shadow-medium hover:shadow-large'
    };
    
    const sizes = {
      sm: 'px-4 py-2 text-sm rounded-lg',
      md: 'px-6 py-3 text-sm rounded-xl',
      lg: 'px-8 py-4 text-base rounded-xl',
      xl: 'px-10 py-5 text-lg rounded-2xl'
    };

    return (
      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
        <button
          ref={ref}
          className={cn(baseClasses, variants[variant], sizes[size], className)}
          {...props}
        >
          {loading && (
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          )}
          {children}
        </button>
      </motion.div>
    );
  }
);

Button.displayName = 'Button';

export { Button };
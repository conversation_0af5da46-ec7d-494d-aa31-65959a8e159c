'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Zap, 
  Globe, 
  Smartphone, 
  BarChart3, 
  Mail, 
  MessageSquare,
  Calendar,
  CreditCard,
  Database,
  Cloud,
  Shield,
  Settings,
  ArrowRight,
  Sparkles
} from 'lucide-react';

const integrations = [
  {
    name: 'Google Analytics',
    category: 'Analytics',
    icon: BarChart3,
    color: 'from-orange-500 to-red-500',
    description: 'Track campaign performance with detailed analytics',
    position: { x: 0, y: 0, z: 0 }
  },
  {
    name: 'Facebook Ads',
    category: 'Social Media',
    icon: Globe,
    color: 'from-blue-500 to-indigo-500',
    description: 'Manage social media advertising campaigns',
    position: { x: 1, y: 0, z: 1 }
  },
  {
    name: 'WhatsApp Business',
    category: 'Communication',
    icon: MessageSquare,
    color: 'from-green-500 to-emerald-500',
    description: 'Direct customer communication and support',
    position: { x: -1, y: 1, z: 0 }
  },
  {
    name: 'Stripe Payments',
    category: 'Payments',
    icon: CreditCard,
    color: 'from-purple-500 to-pink-500',
    description: 'Secure payment processing and billing',
    position: { x: 0, y: -1, z: 1 }
  },
  {
    name: 'Salesforce CRM',
    category: 'CRM',
    icon: Database,
    color: 'from-cyan-500 to-blue-500',
    description: 'Customer relationship management integration',
    position: { x: 1, y: 1, z: -1 }
  },
  {
    name: 'AWS Cloud',
    category: 'Infrastructure',
    icon: Cloud,
    color: 'from-yellow-500 to-orange-500',
    description: 'Scalable cloud infrastructure and storage',
    position: { x: -1, y: -1, z: 1 }
  },
  {
    name: 'Zapier',
    category: 'Automation',
    icon: Zap,
    color: 'from-indigo-500 to-purple-500',
    description: 'Workflow automation and app connections',
    position: { x: 0, y: 1, z: -1 }
  },
  {
    name: 'Google Calendar',
    category: 'Scheduling',
    icon: Calendar,
    color: 'from-red-500 to-pink-500',
    description: 'Campaign scheduling and timeline management',
    position: { x: -1, y: 0, z: -1 }
  }
];

const categories = ['All', 'Analytics', 'Social Media', 'Communication', 'Payments', 'CRM', 'Infrastructure', 'Automation'];

export default function Integrations3D() {
  const [activeCategory, setActiveCategory] = useState('All');
  const [hoveredIntegration, setHoveredIntegration] = useState<string | null>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const filteredIntegrations = activeCategory === 'All' 
    ? integrations 
    : integrations.filter(integration => integration.category === activeCategory);

  return (
    <section 
      ref={sectionRef} 
      className="section-spacing scroll-optimized transform-gpu will-change-transform relative overflow-hidden" 
      style={{ background: 'var(--linear-bg-primary)' }}
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-green-500/10 to-cyan-500/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl" />
      </div>

      <div className="content-container relative z-10">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge purple mb-6"
          >
            <Sparkles className="w-4 h-4" />
            Integrations
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Connect Everything You Use
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="linear-text-lg max-w-3xl mx-auto"
            style={{ color: 'var(--linear-text-secondary)' }}
          >
            Seamlessly integrate with your favorite tools and platforms. Our extensive integration ecosystem ensures your workflow remains uninterrupted.
          </motion.p>
        </div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-16"
        >
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                activeCategory === category 
                  ? 'bg-white/10 border border-white/20' 
                  : 'hover:bg-white/5'
              }`}
              style={{ 
                color: activeCategory === category 
                  ? 'var(--linear-text-primary)' 
                  : 'var(--linear-text-secondary)' 
              }}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* 3D Integration Grid */}
        <div className="relative">
          {/* Central Hub */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20"
          >
            <div 
              className="w-24 h-24 rounded-2xl flex items-center justify-center relative"
              style={{ background: 'var(--linear-bg-secondary)', border: '2px solid var(--linear-border)' }}
            >
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <Settings className="w-8 h-8 text-white" />
              </div>
              
              {/* Pulsing Ring */}
              <motion.div
                animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 rounded-2xl border-2 border-blue-500/30"
              />
            </div>
          </motion.div>

          {/* Integration Cards in 3D Layout */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 min-h-[600px] items-center">
            {filteredIntegrations.map((integration, index) => {
              const Icon = integration.icon;
              const delay = 0.5 + index * 0.1;
              
              return (
                <motion.div
                  key={integration.name}
                  initial={{ opacity: 0, y: 50, rotateX: -15 }}
                  whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                  transition={{ duration: 0.6, delay }}
                  viewport={{ once: true }}
                  className="relative group cursor-pointer"
                  onMouseEnter={() => setHoveredIntegration(integration.name)}
                  onMouseLeave={() => setHoveredIntegration(null)}
                  style={{
                    transform: `translate3d(${integration.position.x * 20}px, ${integration.position.y * 20}px, ${integration.position.z * 20}px)`,
                    transformStyle: 'preserve-3d'
                  }}
                >
                  {/* Connection Line to Center */}
                  <motion.div
                    initial={{ scaleX: 0 }}
                    whileInView={{ scaleX: 1 }}
                    transition={{ duration: 0.8, delay: delay + 0.2 }}
                    viewport={{ once: true }}
                    className="absolute top-1/2 left-1/2 w-32 h-0.5 bg-gradient-to-r from-transparent via-white/20 to-transparent origin-left transform -translate-y-1/2 pointer-events-none"
                    style={{
                      transform: `translate(-50%, -50%) rotate(${Math.atan2(integration.position.y, integration.position.x) * 180 / Math.PI}deg)`
                    }}
                  />

                  {/* Integration Card */}
                  <motion.div
                    whileHover={{ 
                      scale: 1.05, 
                      rotateY: 5,
                      z: 50
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="linear-card p-6 rounded-2xl text-center relative overflow-hidden"
                    style={{ 
                      background: 'var(--linear-bg-secondary)', 
                      border: '1px solid var(--linear-border)',
                      transformStyle: 'preserve-3d'
                    }}
                  >
                    {/* Gradient Overlay */}
                    <div 
                      className={`absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 bg-gradient-to-br ${integration.color}`}
                    />
                    
                    {/* Icon */}
                    <div 
                      className={`w-16 h-16 mx-auto mb-4 rounded-xl flex items-center justify-center bg-gradient-to-br ${integration.color} relative`}
                    >
                      <Icon className="w-8 h-8 text-white" />
                      
                      {/* Floating particles */}
                      {hoveredIntegration === integration.name && (
                        <>
                          {[...Array(3)].map((_, i) => (
                            <motion.div
                              key={i}
                              initial={{ opacity: 0, scale: 0 }}
                              animate={{ 
                                opacity: [0, 1, 0], 
                                scale: [0, 1, 0],
                                x: [0, (i - 1) * 20],
                                y: [0, -20 - i * 10]
                              }}
                              transition={{ 
                                duration: 1.5, 
                                repeat: Infinity,
                                delay: i * 0.2
                              }}
                              className="absolute w-1 h-1 bg-white rounded-full"
                            />
                          ))}
                        </>
                      )}
                    </div>
                    
                    {/* Content */}
                    <h3 className="font-semibold mb-2" style={{ color: 'var(--linear-text-primary)' }}>
                      {integration.name}
                    </h3>
                    <p className="text-xs mb-3" style={{ color: 'var(--linear-text-muted)' }}>
                      {integration.category}
                    </p>
                    <p className="text-sm leading-relaxed" style={{ color: 'var(--linear-text-secondary)' }}>
                      {integration.description}
                    </p>

                    {/* Hover Effect */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      whileHover={{ opacity: 1, y: 0 }}
                      className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
                    >
                      <ArrowRight className="w-4 h-4" style={{ color: 'var(--linear-text-muted)' }} />
                    </motion.div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg mb-6" style={{ color: 'var(--linear-text-secondary)' }}>
            Don't see your favorite tool? We're constantly adding new integrations.
          </p>
          <button className="btn-primary-linear">
            Request Integration
          </button>
        </motion.div>
      </div>
    </section>
  );
}

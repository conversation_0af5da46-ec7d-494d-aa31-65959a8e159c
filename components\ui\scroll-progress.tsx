'use client';

import { motion, useScroll, useSpring } from 'framer-motion';
import { useScrollProgress } from '@/lib/advanced-animations';

export function ScrollProgress() {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  return (
    <motion.div
      className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 via-indigo-500 to-cyan-500 origin-left z-50"
      style={{ scaleX }}
    />
  );
}

export function CircularScrollProgress() {
  const scrollProgress = useScrollProgress();
  const circumference = 2 * Math.PI * 20; // radius = 20

  return (
    <div className="fixed bottom-8 right-8 z-50">
      <div className="relative w-12 h-12">
        <svg
          className="w-12 h-12 transform -rotate-90"
          viewBox="0 0 44 44"
        >
          {/* Background circle */}
          <circle
            cx="22"
            cy="22"
            r="20"
            stroke="rgba(255, 255, 255, 0.1)"
            strokeWidth="2"
            fill="none"
            className="backdrop-blur-sm"
          />
          {/* Progress circle */}
          <motion.circle
            cx="22"
            cy="22"
            r="20"
            stroke="url(#gradient)"
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            strokeDashoffset={circumference * (1 - scrollProgress)}
            className="transition-all duration-300 ease-out"
          />
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" />
              <stop offset="50%" stopColor="#6366f1" />
              <stop offset="100%" stopColor="#06b6d4" />
            </linearGradient>
          </defs>
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 rounded-full glass-strong flex items-center justify-center">
            <motion.div
              className="text-xs font-semibold text-white"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {Math.round(scrollProgress * 100)}%
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function SectionProgress() {
  const sections = ['hero', 'features', 'pricing', 'testimonials', 'faq', 'contact'];
  const scrollProgress = useScrollProgress();
  
  // Calculate which section is currently active
  const activeSection = Math.floor(scrollProgress * sections.length);

  return (
    <div className="fixed left-8 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block">
      <div className="flex flex-col space-y-4">
        {sections.map((section, index) => (
          <motion.button
            key={section}
            onClick={() => {
              const element = document.getElementById(section);
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="group relative"
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          >
            <div
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index <= activeSection
                  ? 'bg-gradient-to-r from-purple-500 to-indigo-500 shadow-glow'
                  : 'bg-white/20 backdrop-blur-sm border border-white/30'
              }`}
            />
            
            {/* Tooltip */}
            <div className="absolute left-6 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
              <div className="glass-strong rounded-lg px-3 py-1 text-sm text-white capitalize whitespace-nowrap">
                {section}
              </div>
            </div>
          </motion.button>
        ))}
      </div>
    </div>
  );
}

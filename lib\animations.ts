export function initScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -100px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
      }
    });
  }, observerOptions);

  // Observe all elements with animation classes
  const animatedElements = document.querySelectorAll(
    '.animate-fade-in-up, .animate-fade-in-left, .animate-fade-in-right'
  );
  
  animatedElements.forEach((element) => {
    observer.observe(element);
  });
}

export function addParallaxEffect() {
  window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    const parallaxElements = document.querySelectorAll('.parallax');
    parallaxElements.forEach((element) => {
      (element as HTMLElement).style.transform = `translateY(${rate}px)`;
    });
  });
}
import { ReactNode } from 'react';
import { GlassCard } from './glass-card';
import { AnimatedButton } from './animated-button';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PricingCardProps {
  title: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  buttonText: string;
  buttonVariant?: 'primary' | 'secondary' | 'outline';
  popular?: boolean;
  className?: string;
}

export function PricingCard({ 
  title, 
  price, 
  period, 
  description, 
  features, 
  buttonText, 
  buttonVariant = 'outline',
  popular = false,
  className 
}: PricingCardProps) {
  return (
    <div className={cn('relative', className)}>
      {popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
            Most Popular
          </div>
        </div>
      )}
      <GlassCard className={cn(
        'p-8 h-full flex flex-col',
        popular ? 'ring-2 ring-blue-500/50 bg-white/30' : ''
      )}>
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold mb-2" style={{ color: 'var(--linear-text-primary)' }}>{title}</h3>
          <p className="mb-4" style={{ color: 'var(--linear-text-secondary)' }}>{description}</p>
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold" style={{ color: 'var(--linear-text-primary)' }}>{price}</span>
            <span className="ml-2" style={{ color: 'var(--linear-text-secondary)' }}>/{period}</span>
          </div>
        </div>
        
        <div className="flex-1 mb-8">
          <ul className="space-y-4">
            {features.map((feature, index) => (
              <li key={index} className="flex items-start space-x-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span style={{ color: 'var(--linear-text-secondary)' }}>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <AnimatedButton 
          variant={buttonVariant}
          size="lg" 
          className="w-full"
        >
          {buttonText}
        </AnimatedButton>
      </GlassCard>
    </div>
  );
}
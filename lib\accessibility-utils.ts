'use client';

import { useEffect, useState, useRef } from 'react';

// Detect user's motion preferences
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// Focus management utilities
export const focusManagement = {
  // Trap focus within an element
  trapFocus: (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    element.addEventListener('keydown', handleTabKey);
    firstElement.focus();

    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  },

  // Restore focus to previous element
  restoreFocus: (previousElement: HTMLElement | null) => {
    if (previousElement) {
      previousElement.focus();
    }
  },

  // Focus visible utility
  addFocusVisible: () => {
    let hadKeyboardEvent = true;
    const keyboardThrottleTimeout = 100;

    const pointerEvent = () => {
      hadKeyboardEvent = false;
    };

    const keyboardEvent = (e: KeyboardEvent) => {
      if (e.metaKey || e.altKey || e.ctrlKey) {
        return;
      }
      hadKeyboardEvent = true;
    };

    const focusEvent = (e: FocusEvent) => {
      if (hadKeyboardEvent || (e.target as HTMLElement).matches(':focus-visible')) {
        (e.target as HTMLElement).classList.add('focus-visible');
      }
    };

    const blurEvent = (e: FocusEvent) => {
      (e.target as HTMLElement).classList.remove('focus-visible');
    };

    document.addEventListener('keydown', keyboardEvent, true);
    document.addEventListener('mousedown', pointerEvent, true);
    document.addEventListener('pointerdown', pointerEvent, true);
    document.addEventListener('touchstart', pointerEvent, true);
    document.addEventListener('focus', focusEvent, true);
    document.addEventListener('blur', blurEvent, true);

    return () => {
      document.removeEventListener('keydown', keyboardEvent, true);
      document.removeEventListener('mousedown', pointerEvent, true);
      document.removeEventListener('pointerdown', pointerEvent, true);
      document.removeEventListener('touchstart', pointerEvent, true);
      document.removeEventListener('focus', focusEvent, true);
      document.removeEventListener('blur', blurEvent, true);
    };
  },
};

// Keyboard navigation utilities
export const keyboardNavigation = {
  // Handle arrow key navigation
  handleArrowKeys: (
    elements: HTMLElement[],
    currentIndex: number,
    key: string
  ): number => {
    switch (key) {
      case 'ArrowDown':
      case 'ArrowRight':
        return (currentIndex + 1) % elements.length;
      case 'ArrowUp':
      case 'ArrowLeft':
        return currentIndex === 0 ? elements.length - 1 : currentIndex - 1;
      default:
        return currentIndex;
    }
  },

  // Handle escape key
  handleEscape: (callback: () => void) => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        callback();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  },

  // Handle enter and space keys
  handleActivation: (callback: () => void) => {
    return (e: KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        callback();
      }
    };
  },
};

// Screen reader utilities
export const screenReader = {
  // Announce to screen readers
  announce: (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },

  // Create visually hidden text class
  getVisuallyHiddenClass: () => 'sr-only',

  // Get skip link classes
  getSkipLinkClasses: () => 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded z-50',
};

// Color contrast utilities
export const colorContrast = {
  // Calculate relative luminance
  getLuminance: (r: number, g: number, b: number): number => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  },

  // Calculate contrast ratio
  getContrastRatio: (color1: [number, number, number], color2: [number, number, number]): number => {
    const lum1 = colorContrast.getLuminance(...color1);
    const lum2 = colorContrast.getLuminance(...color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  // Check if contrast meets WCAG standards
  meetsWCAG: (
    color1: [number, number, number], 
    color2: [number, number, number], 
    level: 'AA' | 'AAA' = 'AA'
  ): boolean => {
    const ratio = colorContrast.getContrastRatio(color1, color2);
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  },
};

// ARIA utilities
export const aria = {
  // Generate unique IDs for ARIA relationships
  useId: (prefix: string = 'id') => {
    const [id] = useState(() => `${prefix}-${Math.random().toString(36).substr(2, 9)}`);
    return id;
  },

  // ARIA live region hook
  useLiveRegion: () => {
    const [message, setMessage] = useState('');
    const [priority, setPriority] = useState<'polite' | 'assertive'>('polite');

    const announce = (text: string, level: 'polite' | 'assertive' = 'polite') => {
      setMessage(text);
      setPriority(level);
      
      // Clear message after announcement
      setTimeout(() => setMessage(''), 100);
    };

    const getLiveRegionProps = () => ({
      'aria-live': priority,
      'aria-atomic': 'true',
      className: 'sr-only',
      children: message
    });

    return { announce, getLiveRegionProps, message };
  },

  // ARIA expanded state
  useExpanded: (initialState: boolean = false) => {
    const [expanded, setExpanded] = useState(initialState);
    
    const toggle = () => setExpanded(!expanded);
    const expand = () => setExpanded(true);
    const collapse = () => setExpanded(false);

    return {
      expanded,
      toggle,
      expand,
      collapse,
      ariaExpanded: expanded,
    };
  },
};

// Form accessibility utilities
export const formAccessibility = {
  // Generate form field IDs and relationships
  useFormField: (name: string) => {
    const fieldId = aria.useId(`field-${name}`);
    const errorId = aria.useId(`error-${name}`);
    const helpId = aria.useId(`help-${name}`);

    return {
      fieldId,
      errorId,
      helpId,
      getFieldProps: (hasError: boolean = false, hasHelp: boolean = false) => ({
        id: fieldId,
        'aria-invalid': hasError,
        'aria-describedby': [
          hasError ? errorId : null,
          hasHelp ? helpId : null,
        ].filter(Boolean).join(' ') || undefined,
      }),
      getLabelProps: () => ({
        htmlFor: fieldId,
      }),
      getErrorProps: () => ({
        id: errorId,
        role: 'alert',
        'aria-live': 'polite' as const,
      }),
      getHelpProps: () => ({
        id: helpId,
      }),
    };
  },

  // Validate form accessibility
  validateFormAccessibility: (formElement: HTMLFormElement) => {
    const issues: string[] = [];
    
    // Check for labels
    const inputs = formElement.querySelectorAll('input, select, textarea');
    inputs.forEach((input) => {
      const id = input.getAttribute('id');
      const label = formElement.querySelector(`label[for="${id}"]`);
      const ariaLabel = input.getAttribute('aria-label');
      const ariaLabelledby = input.getAttribute('aria-labelledby');
      
      if (!label && !ariaLabel && !ariaLabelledby) {
        issues.push(`Input ${id || 'without ID'} is missing a label`);
      }
    });

    // Check for required field indicators
    const requiredInputs = formElement.querySelectorAll('[required]');
    requiredInputs.forEach((input) => {
      const ariaRequired = input.getAttribute('aria-required');
      if (!ariaRequired) {
        issues.push(`Required input ${input.getAttribute('id') || 'without ID'} should have aria-required="true"`);
      }
    });

    return issues;
  },
};

// High contrast mode detection
export const useHighContrast = () => {
  const [highContrast, setHighContrast] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setHighContrast(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setHighContrast(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return highContrast;
};

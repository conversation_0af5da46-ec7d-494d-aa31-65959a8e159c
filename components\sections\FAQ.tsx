'use client';

import { useState, useRef, useMemo } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Plus, Minus, Search, HelpCircle, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

const faqs = [
  {
    category: "Product",
    question: "What makes ADmyBRAND different from other marketing tools?",
    answer: "ADmyBRAND combines advanced AI with deep marketing expertise. Our proprietary algorithms understand brand voice, predict customer behavior, and optimize campaigns in real-time. Unlike generic tools, we're built specifically for marketing professionals who need enterprise-grade AI capabilities.",
    tags: ["AI", "features", "comparison"]
  },
  {
    category: "Results",
    question: "How quickly can I see results?",
    answer: "Most customers see significant improvements within the first week. Our AI starts learning your brand voice immediately, and campaign optimizations begin showing results within 24-48 hours. Typically, customers report 2-3x ROI improvements within the first month.",
    tags: ["timeline", "ROI", "results"]
  },
  {
    category: "Getting Started",
    question: "Do I need technical knowledge to use the platform?",
    answer: "Not at all! ADmyBRAND is designed for marketers, not developers. Our intuitive interface guides you through setup, and our AI handles the complex technical work. We also provide comprehensive onboarding and support to ensure your success.",
    tags: ["ease of use", "setup", "technical"]
  },
  {
    category: "Integrations",
    question: "Can ADmyBRAND integrate with my existing tools?",
    answer: "Yes! We integrate with 100+ popular marketing tools including Google Ads, Facebook Ads, HubSpot, Salesforce, Mailchimp, and more. Our API also allows custom integrations for enterprise customers with unique requirements.",
    tags: ["integrations", "API", "tools"]
  },
  {
    category: "Security",
    question: "Is my data secure?",
    answer: "Absolutely. We use enterprise-grade security with SOC 2 Type II compliance, end-to-end encryption, and regular security audits. Your data is never shared with third parties, and we offer GDPR-compliant analytics to respect user privacy.",
    tags: ["security", "privacy", "compliance"]
  },
  {
    category: "Support",
    question: "What kind of support do you provide?",
    answer: "We offer comprehensive support including 24/7 live chat, extensive documentation, video tutorials, and webinar training. Professional and Enterprise customers get priority support and dedicated account managers.",
    tags: ["support", "help", "training"]
  },
  {
    category: "Pricing",
    question: "Can I try ADmyBRAND before purchasing?",
    answer: "Yes! We offer a 14-day free trial with full access to all features. No credit card required. You can also schedule a personalized demo with our team to see how ADmyBRAND can transform your marketing strategy.",
    tags: ["trial", "demo", "pricing"]
  },
  {
    category: "Features",
    question: "What AI capabilities does ADmyBRAND offer?",
    answer: "Our AI suite includes predictive analytics, automated content generation, brand voice analysis, customer segmentation, campaign optimization, and real-time performance insights. All powered by machine learning that continuously improves with your data.",
    tags: ["AI", "features", "capabilities"]
  }
];

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const categories = ['All', ...Array.from(new Set(faqs.map(faq => faq.category)))];

  const filteredFAQs = useMemo(() => {
    return faqs.filter(faq => {
      const matchesSearch = searchQuery === '' ||
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="section-padding relative overflow-hidden" ref={ref} style={{ background: 'var(--linear-bg-primary)' }}>
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.02)_1px,transparent_1px)] bg-[size:48px_48px]" />

        {/* Floating question marks */}
        <motion.div
          className="absolute top-20 right-10 w-20 h-20 bg-gradient-to-r from-purple-400/10 to-indigo-400/10 rounded-full blur-xl"
          animate={{
            y: [-25, 25, -25],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-16 h-16 bg-gradient-to-r from-cyan-400/10 to-blue-400/10 rounded-xl blur-lg"
          animate={{
            y: [25, -25, 25],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="max-width-content mx-auto container-padding relative z-10">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="inline-flex items-center space-x-3 glass-subtle rounded-full px-6 py-3 mb-8 border border-white/20"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <HelpCircle className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-gray-700">FAQ</span>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 text-balance"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Frequently asked{' '}
            <span className="bg-gradient-to-r from-purple-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent animate-gradient bg-[length:200%_auto]">
              questions
            </span>
          </motion.h2>

          <motion.p
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed text-pretty mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            Everything you need to know about ADmyBRAND. Can't find the answer you're looking for?
            Our friendly team is here to help.
          </motion.p>

          {/* Search Bar */}
          <motion.div
            className="max-w-md mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 glass-strong rounded-2xl border border-white/20 focus:outline-none focus:ring-2 focus:ring-purple-500/20 focus:border-purple-300 transition-all duration-300 text-gray-900 placeholder-gray-500"
              />
            </div>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            className="flex flex-wrap items-center justify-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            {categories.map((category, index) => (
              <motion.button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-glow'
                    : 'glass-subtle border border-white/20 text-gray-600 hover:text-gray-900 hover:glass-strong'
                }`}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={isInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.4, delay: 1.0 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {category}
              </motion.button>
            ))}
          </motion.div>
        </motion.div>

        {/* Enhanced FAQ Items */}
        <motion.div
          className="max-w-4xl mx-auto space-y-6"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <AnimatePresence>
            {filteredFAQs.length === 0 ? (
              <motion.div
                className="text-center py-12"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No questions found matching your search.</p>
                <Button
                  variant="secondary"
                  className="mt-4"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                  }}
                >
                  Clear filters
                </Button>
              </motion.div>
            ) : (
              filteredFAQs.map((faq, index) => (
                <motion.div
                  key={`${faq.question}-${index}`}
                  className="glass-strong rounded-2xl border border-white/20 overflow-hidden shadow-colored hover:shadow-glow transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.2 + index * 0.1 }}
                  layout
                >
                  <motion.button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-6 text-left flex items-center justify-between hover:glass-strong transition-all duration-300 group"
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <div className="flex-1 pr-4">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="px-3 py-1 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 text-purple-700 text-xs font-medium rounded-full border border-purple-200/50">
                          {faq.category}
                        </span>
                      </div>
                      <span className="font-semibold text-gray-900 text-lg group-hover:text-purple-700 transition-colors duration-200">
                        {faq.question}
                      </span>
                    </div>
                    <motion.div
                      animate={{ rotate: openIndex === index ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                      className="flex-shrink-0"
                    >
                      {openIndex === index ? (
                        <Minus className="h-6 w-6 text-purple-600" />
                      ) : (
                        <Plus className="h-6 w-6 text-gray-500 group-hover:text-purple-600 transition-colors duration-200" />
                      )}
                    </motion.div>
                  </motion.button>

                  <AnimatePresence>
                    {openIndex === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6 border-t border-white/10">
                          <motion.p
                            className="text-gray-700 leading-relaxed pt-4 text-pretty"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                          >
                            {faq.answer}
                          </motion.p>

                          {/* Tags */}
                          <motion.div
                            className="flex flex-wrap gap-2 mt-4"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: 0.2 }}
                          >
                            {faq.tags.map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-lg"
                              >
                                {tag}
                              </span>
                            ))}
                          </motion.div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </motion.div>

        {/* Enhanced CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.4 }}
        >
          <div className="glass-strong rounded-3xl p-8 lg:p-12 border border-white/20 max-w-2xl mx-auto">
            <motion.div
              className="w-16 h-16 rounded-2xl bg-gradient-to-r from-purple-500 to-indigo-500 flex items-center justify-center mx-auto mb-6"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.3 }}
            >
              <MessageCircle className="h-8 w-8 text-white" />
            </motion.div>

            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-8 text-pretty">
              Can't find the answer you're looking for? Our friendly team is here to help.
              Get in touch and we'll get back to you as soon as possible.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="gradient" size="lg" className="shadow-glow">
                <MessageCircle className="mr-2 h-5 w-5" />
                Contact Support
              </Button>
              <Button variant="secondary" size="lg" className="glass-subtle border-white/20">
                Schedule Demo
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
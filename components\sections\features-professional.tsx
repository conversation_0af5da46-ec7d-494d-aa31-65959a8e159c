'use client';

import { motion } from 'framer-motion';
import { useState, useRef } from 'react';
import {
  Brain,
  BarChart3,
  Zap,
  Target,
  Users,
  TrendingUp,
  Shield,
  Clock,
  ArrowRight,
  MapPin,
  Radio,
  Tv,
  Smartphone,
  Monitor,
  Map,
  Navigation,
  Activity,
  Calendar,
  Filter,
  Eye,
  MousePointer
} from 'lucide-react';

// Linear-style visual components for ADmyBRAND features
const OutdoorMediaVisual = () => (
  <div className="linear-interface-mini">
    <div className="linear-interface-header-mini">
      <div className="linear-interface-dots-mini">
        <div className="linear-interface-dot-mini red"></div>
        <div className="linear-interface-dot-mini yellow"></div>
        <div className="linear-interface-dot-mini green"></div>
      </div>
      <span className="linear-body-xs">Outdoor Media Dashboard</span>
    </div>
    <div className="linear-interface-content-mini">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4" style={{ color: 'var(--linear-text-secondary)' }} />
            <span className="linear-body-sm">Mumbai Central</span>
          </div>
          <div className="linear-badge green-mini">Available</div>
        </div>
        <div className="linear-progress-bar">
          <div className="linear-progress-fill" style={{ width: '73%' }}></div>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <div className="linear-metric-mini">
            <div className="linear-body-xs">Reach</div>
            <div className="linear-heading-sm">2.4M</div>
          </div>
          <div className="linear-metric-mini">
            <div className="linear-body-xs">Cost</div>
            <div className="linear-heading-sm">₹45K</div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Enhanced Digital Advertising Dashboard with Real-time Analytics
const DigitalAdsVisual = () => (
  <div className="linear-interface-mini">
    <div className="linear-interface-header-mini">
      <div className="linear-interface-dots-mini">
        <div className="linear-interface-dot-mini red"></div>
        <div className="linear-interface-dot-mini yellow"></div>
        <div className="linear-interface-dot-mini green"></div>
      </div>
      <span className="linear-body-xs">Campaign Analytics</span>
    </div>
    <div className="linear-interface-content-mini">
      <div className="space-y-3">
        {/* Live campaign status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="linear-body-xs">47 Live Campaigns</span>
          </div>
          <div className="linear-badge green-mini">₹2.4L spent</div>
        </div>

        {/* Interactive performance chart */}
        <div className="linear-chart-mini">
          <div className="linear-chart-bars">
            <motion.div
              className="linear-chart-bar"
              style={{ height: '60%', background: 'var(--linear-text-secondary)' }}
              initial={{ height: 0 }}
              animate={{ height: '60%' }}
              transition={{ delay: 0.1 }}
            />
            <motion.div
              className="linear-chart-bar"
              style={{ height: '80%', background: 'var(--linear-text-secondary)' }}
              initial={{ height: 0 }}
              animate={{ height: '80%' }}
              transition={{ delay: 0.2 }}
            />
            <motion.div
              className="linear-chart-bar"
              style={{ height: '45%', background: 'var(--linear-text-secondary)' }}
              initial={{ height: 0 }}
              animate={{ height: '45%' }}
              transition={{ delay: 0.3 }}
            />
            <motion.div
              className="linear-chart-bar"
              style={{ height: '90%', background: 'var(--linear-text-secondary)' }}
              initial={{ height: 0 }}
              animate={{ height: '90%' }}
              transition={{ delay: 0.4 }}
            />
            <motion.div
              className="linear-chart-bar"
              style={{ height: '70%', background: 'var(--linear-text-secondary)' }}
              initial={{ height: 0 }}
              animate={{ height: '70%' }}
              transition={{ delay: 0.5 }}
            />
          </div>
        </div>

        {/* Key metrics with trends */}
        <div className="grid grid-cols-3 gap-2">
          <div className="linear-metric-mini">
            <div className="linear-body-xs">CTR</div>
            <div className="linear-heading-sm text-green-400">4.2%</div>
            <div className="linear-body-xs text-green-400">↑ 12%</div>
          </div>
          <div className="linear-metric-mini">
            <div className="linear-body-xs">CPC</div>
            <div className="linear-heading-sm">₹8.50</div>
            <div className="linear-body-xs text-green-400">↓ 15%</div>
          </div>
          <div className="linear-metric-mini">
            <div className="linear-body-xs">ROAS</div>
            <div className="linear-heading-sm" style={{ color: 'var(--linear-text-primary)' }}>5.8x</div>
            <div className="linear-body-xs text-green-400">↑ 23%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Enhanced Traditional Media Planning with Interactive Map
const TraditionalMediaVisual = () => (
  <div className="linear-interface-mini">
    <div className="linear-interface-header-mini">
      <div className="linear-interface-dots-mini">
        <div className="linear-interface-dot-mini red"></div>
        <div className="linear-interface-dot-mini yellow"></div>
        <div className="linear-interface-dot-mini green"></div>
      </div>
      <span className="linear-body-xs">Media Planning Hub</span>
    </div>
    <div className="linear-interface-content-mini">
      <div className="space-y-3">
        {/* Interactive media type selector */}
        <div className="grid grid-cols-2 gap-2">
          <div className="linear-media-card active">
            <Radio className="w-3 h-3 mb-1" style={{ color: 'var(--linear-orange)' }} />
            <div className="linear-body-xs">Radio</div>
            <div className="linear-body-xs text-green-400">127 stations</div>
          </div>
          <div className="linear-media-card">
            <Tv className="w-3 h-3 mb-1" style={{ color: 'var(--linear-red)' }} />
            <div className="linear-body-xs">TV</div>
            <div className="linear-body-xs text-blue-400">Scheduled</div>
          </div>
          <div className="linear-media-card">
            <Monitor className="w-3 h-3 mb-1" style={{ color: 'var(--linear-green)' }} />
            <div className="linear-body-xs">Print</div>
            <div className="linear-body-xs text-yellow-400">Pending</div>
          </div>
        </div>
        <div className="linear-timeline-mini">
          <div className="linear-timeline-item active"></div>
          <div className="linear-timeline-item active"></div>
          <div className="linear-timeline-item"></div>
          <div className="linear-timeline-item"></div>
        </div>
      </div>
    </div>
  </div>
);

// Innovative Outdoor Advertising Map Interface
const OutdoorAdvertisingMapVisual = () => (
  <div className="linear-interface-mini">
    <div className="linear-interface-header-mini">
      <div className="linear-interface-dots-mini">
        <div className="linear-interface-dot-mini red"></div>
        <div className="linear-interface-dot-mini yellow"></div>
        <div className="linear-interface-dot-mini green"></div>
      </div>
      <span className="linear-body-xs">Outdoor Media Map</span>
    </div>
    <div className="linear-interface-content-mini">
      <div className="space-y-3">
        {/* Interactive map with location pins */}
        <div className="relative h-16 rounded" style={{ background: 'var(--linear-bg-tertiary)', border: '1px solid var(--linear-border)' }}>
          {/* Simulated map pins */}
          <motion.div
            className="absolute top-2 left-3 w-2 h-2 bg-red-400 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
          <motion.div
            className="absolute top-4 right-4 w-2 h-2 bg-green-400 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
          />
          <motion.div
            className="absolute bottom-3 left-6 w-2 h-2 bg-blue-400 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 2, repeat: Infinity, delay: 1 }}
          />
          <motion.div
            className="absolute bottom-2 right-2 w-2 h-2 bg-yellow-400 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
          />

          {/* Map overlay info */}
          <div className="absolute top-1 right-1 linear-badge green-mini text-xs">
            2,847 spots
          </div>
        </div>

        {/* Location filters */}
        <div className="flex gap-1">
          <div className="linear-filter-chip active">
            <MapPin className="w-2 h-2" />
            <span className="text-xs">Mumbai</span>
          </div>
          <div className="linear-filter-chip">
            <span className="text-xs">Delhi</span>
          </div>
          <div className="linear-filter-chip">
            <span className="text-xs">+15</span>
          </div>
        </div>

        {/* Real-time availability */}
        <div className="grid grid-cols-2 gap-2">
          <div className="linear-metric-mini">
            <div className="linear-body-xs">Available</div>
            <div className="linear-heading-sm text-green-400">1,247</div>
          </div>
          <div className="linear-metric-mini">
            <div className="linear-body-xs">Avg Cost</div>
            <div className="linear-heading-sm">₹45K</div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Advanced Analytics Dashboard Interface
const AnalyticsDashboardVisual = () => (
  <div className="linear-interface-mini">
    <div className="linear-interface-header-mini">
      <div className="linear-interface-dots-mini">
        <div className="linear-interface-dot-mini red"></div>
        <div className="linear-interface-dot-mini yellow"></div>
        <div className="linear-interface-dot-mini green"></div>
      </div>
      <span className="linear-body-xs">Campaign Analytics</span>
    </div>
    <div className="linear-interface-content-mini">
      <div className="space-y-3">
        {/* Real-time performance indicators */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Activity className="w-3 h-3 text-green-400" />
            <span className="linear-body-xs">Live Performance</span>
          </div>
          <div className="linear-badge green-mini">
            <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse mr-1"></div>
            Real-time
          </div>
        </div>

        {/* Multi-channel performance chart */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="linear-body-xs">Outdoor</span>
            <div className="flex items-center gap-1">
              <div className="w-8 h-1 rounded" style={{ background: 'var(--linear-text-secondary)' }}></div>
              <span className="linear-body-xs">85%</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="linear-body-xs">Digital</span>
            <div className="flex items-center gap-1">
              <div className="w-6 h-1 rounded" style={{ background: 'var(--linear-text-secondary)' }}></div>
              <span className="linear-body-xs">72%</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="linear-body-xs">Print</span>
            <div className="flex items-center gap-1">
              <div className="w-4 h-1 rounded" style={{ background: 'var(--linear-text-secondary)' }}></div>
              <span className="linear-body-xs">58%</span>
            </div>
          </div>
        </div>

        {/* Key performance metrics */}
        <div className="grid grid-cols-2 gap-2">
          <div className="linear-metric-mini">
            <div className="linear-body-xs">Reach</div>
            <div className="linear-heading-sm" style={{ color: 'var(--linear-text-primary)' }}>2.4M</div>
          </div>
          <div className="linear-metric-mini">
            <div className="linear-body-xs">ROI</div>
            <div className="linear-heading-sm text-green-400">4.2x</div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const features = [
  {
    icon: MapPin,
    title: 'Outdoor Media Intelligence',
    description: 'Interactive map-based booking of hoardings, billboards, and outdoor spaces with real-time availability and pricing.',
    benefits: ['Interactive Location Maps', 'Real-time Availability', 'Dynamic Pricing', 'Coverage Analytics'],
    visual: OutdoorAdvertisingMapVisual
  },
  {
    icon: BarChart3,
    title: 'Digital Campaign Hub',
    description: 'Advanced digital advertising dashboard with real-time analytics, performance tracking, and AI-powered optimization.',
    benefits: ['Live Performance Tracking', 'AI Optimization', 'Multi-platform Management', 'ROI Analytics'],
    visual: DigitalAdsVisual
  },
  {
    icon: Activity,
    title: 'Unified Analytics Platform',
    description: 'Comprehensive analytics dashboard combining all media channels with advanced reporting and predictive insights.',
    benefits: ['Cross-channel Analytics', 'Predictive Insights', 'Custom Dashboards', 'Real-time Reporting'],
    visual: AnalyticsDashboardVisual
  },
  {
    icon: Radio,
    title: 'Traditional Media Planner',
    description: 'Intelligent planning and booking system for radio, TV, and print media with audience reach optimization.',
    benefits: ['Audience Optimization', 'Media Planning Tools', 'Booking Automation', 'Performance Tracking'],
    visual: TraditionalMediaVisual
  },
  {
    icon: Users,
    title: '10M+ Ad Options',
    description: 'Access to India\'s largest inventory of advertising spaces across all media channels.',
    benefits: ['Massive Inventory', 'Real-time Availability', 'Competitive Pricing']
  },
  {
    icon: TrendingUp,
    title: 'White Label Solutions',
    description: 'Custom portals for agencies and media owners with inventory management and analytics.',
    benefits: ['Agency Portals', 'Inventory Management', 'Custom Analytics']
  }
];

const integrations = [
  { name: '8Hoarding', logo: '🏢' },
  { name: 'Ad8OOH', logo: '📺' },
  { name: '8Digiad', logo: '💻' },
  { name: 'Ad8Mobi', logo: '📱' },
  { name: 'Ad8Paper', logo: '📰' },
  { name: 'Ad8Radio', logo: '📻' },
  { name: 'Ad8TV', logo: '📺' },
  { name: 'Ad8Social', logo: '🌐' }
];

export default function FeaturesProfessional() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  return (
    <section ref={sectionRef} className="section-spacing scroll-optimized transform-gpu will-change-transform" style={{ background: 'var(--linear-bg-primary)' }}>
      <div className="content-container">
        
        {/* Section header */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge mb-6"
          >
            <Zap className="w-4 h-4" />
            Complete Platform
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Everything you need for
            <br />
            <span style={{ color: 'var(--linear-text-primary)' }}>omnichannel advertising</span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="linear-body-lg max-w-3xl mx-auto"
          >
            From outdoor hoardings to digital campaigns, our platform provides
            access to India's largest advertising inventory across all media channels.
          </motion.p>
        </div>

        {/* Optimized Features grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true, margin: "-50px" }}
              className="linear-card group"
              onHoverStart={() => setHoveredIndex(index)}
              onHoverEnd={() => setHoveredIndex(null)}
              whileHover={{
                scale: 1.02,
                transition: { type: "spring", stiffness: 400, damping: 17 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Visual Component */}
              {feature.visual && (
                <div className="mb-6">
                  <feature.visual />
                </div>
              )}

              <motion.div
                className="w-12 h-12 rounded-xl flex items-center justify-center mb-6 transition-colors"
                style={{
                  background: 'var(--linear-bg-tertiary)',
                  border: '1px solid var(--linear-border)'
                }}
                whileHover={{
                  rotate: [0, -10, 10, 0],
                  transition: { duration: 0.5 }
                }}
              >
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  <feature.icon className="w-6 h-6" style={{ color: 'var(--linear-text-primary)' }} />
                </motion.div>
              </motion.div>

              <h3 className="linear-heading-md mb-3">
                {feature.title}
              </h3>

              <p className="linear-body mb-6">
                {feature.description}
              </p>

              <ul className="space-y-2">
                {feature.benefits.map((benefit, idx) => (
                  <li key={idx} className="flex items-center gap-2 linear-body-sm">
                    <div className="w-1.5 h-1.5 rounded-full" style={{ background: 'var(--linear-text-secondary)' }}></div>
                    {benefit}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Integrations section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="linear-heading-lg mb-4">
            Integrates with your favorite tools
          </h3>
          <p className="linear-body mb-12 max-w-2xl mx-auto">
            Connect with 100+ marketing tools and platforms to centralize your data
            and streamline your workflows.
          </p>
          
          <div className="flex flex-wrap justify-center items-center gap-8 mb-12">
            {integrations.map((integration, index) => (
              <motion.div
                key={integration.name}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-3 px-6 py-3 rounded-xl transition-colors"
                style={{
                  background: 'var(--linear-bg-secondary)',
                  border: '1px solid var(--linear-border)'
                }}
              >
                <span className="text-2xl">{integration.logo}</span>
                <span className="linear-body-sm font-medium">{integration.name}</span>
              </motion.div>
            ))}
          </div>
          
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="btn-linear-secondary flex items-center gap-2 mx-auto"
          >
            View all integrations
            <ArrowRight className="w-4 h-4" />
          </motion.button>
        </motion.div>

        {/* Security & Compliance */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-20 p-8 rounded-2xl"
          style={{
            background: 'var(--linear-bg-secondary)',
            border: '1px solid var(--linear-border)'
          }}
        >
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <Shield className="w-8 h-8 mx-auto mb-4" style={{ color: 'var(--linear-text-primary)' }} />
              <h4 className="linear-heading-sm mb-2">Enterprise Security</h4>
              <p className="linear-body-sm">SOC 2 Type II certified with end-to-end encryption</p>
            </div>
            <div>
              <Clock className="w-8 h-8 mx-auto mb-4" style={{ color: 'var(--linear-text-primary)' }} />
              <h4 className="linear-heading-sm mb-2">99.9% Uptime</h4>
              <p className="linear-body-sm">Reliable infrastructure with global CDN</p>
            </div>
            <div>
              <Users className="w-8 h-8 mx-auto mb-4" style={{ color: 'var(--linear-text-primary)' }} />
              <h4 className="linear-heading-sm mb-2">24/7 Support</h4>
              <p className="linear-body-sm">Expert support team available around the clock</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

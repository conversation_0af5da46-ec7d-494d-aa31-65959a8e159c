'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Play, CheckCircle, Sparkles, Search, MapPin, Calendar } from 'lucide-react';
import Navigation from '@/components/ui/navigation';

export default function HeroProfessional() {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    // Handle search functionality
    console.log('Searching for:', searchQuery);
  }, [searchQuery]);

  return (
    <section
      className="relative min-h-screen scroll-optimized transform-gpu will-change-transform"
      style={{ background: 'var(--linear-bg-primary)' }}
    >
      <Navigation />

      {/* Linear-style background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(240, 246, 252, 0.03)' }}></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(240, 246, 252, 0.03)' }}></div>
      </div>
      
      {/* Main content */}
      <div className="relative pt-32 pb-20">
        <div className="content-container">
          <div className="max-w-5xl mx-auto text-center">

            {/* Linear-style badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="linear-badge mb-8"
            >
              <Sparkles className="w-4 h-4" />
              India's largest advertising platform
            </motion.div>

            {/* Linear-style main headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="linear-heading-xl mb-6"
            >
              India's largest advertising platform
              <br />
              ADmyBRAND is a purpose-built tool for
              <br />
              <span style={{ color: 'var(--linear-text-primary)' }}>planning and building advertising</span>
            </motion.h1>

            {/* Linear-style subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="linear-body-lg mb-12 max-w-3xl mx-auto"
              style={{
                lineHeight: '1.5',
                letterSpacing: '-0.011em'
              }}
            >
              Book & advertise on hoardings, outdoor media, mobile, radio, TV, newspapers
              and more. Plan your marketing campaigns from 10M+ ad options.
            </motion.p>

            {/* CTA buttons - Linear style */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
            >
              <motion.button
                className="btn-linear-primary flex items-center gap-2 px-8 py-4 text-base"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                Start building
                <motion.div
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </motion.button>

              <motion.button
                className="btn-linear-secondary flex items-center gap-2 px-8 py-4 text-base"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Play className="w-5 h-5" />
                </motion.div>
                Sell Ad Space
              </motion.button>
            </motion.div>

            {/* Linear-style Interface Mockup */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 40 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="max-w-6xl mx-auto"
            >
              <div className="linear-interface">
                {/* Interface Header */}
                <div className="linear-interface-header">
                  <div className="linear-interface-dots">
                    <div className="linear-interface-dot red"></div>
                    <div className="linear-interface-dot yellow"></div>
                    <div className="linear-interface-dot green"></div>
                  </div>
                  <div className="flex-1 text-center">
                    <span className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>
                      app.admybrand.com
                    </span>
                  </div>
                </div>

                {/* Interface Content */}
                <div className="linear-interface-content">
                  <div className="grid grid-cols-12 gap-6 h-full">
                    {/* Sidebar */}
                    <div className="col-span-3">
                      <div className="space-y-1">
                        <div className="linear-body-sm font-medium mb-4" style={{ color: 'var(--linear-text-primary)' }}>
                          ADmyBRAND
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-3 p-2 rounded-md" style={{ background: 'var(--linear-bg-tertiary)' }}>
                            <div className="w-4 h-4 rounded" style={{ background: 'var(--linear-text-secondary)' }}></div>
                            <span className="linear-body-sm" style={{ color: 'var(--linear-text-primary)' }}>Dashboard</span>
                          </div>
                          <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-800/50 cursor-pointer">
                            <div className="w-4 h-4 rounded bg-blue-500"></div>
                            <span className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>Campaigns</span>
                          </div>
                          <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-800/50 cursor-pointer">
                            <div className="w-4 h-4 rounded bg-green-500"></div>
                            <span className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>Analytics</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Main Content */}
                    <div className="col-span-9">
                      <div className="space-y-6">
                        <div>
                          <h3 className="linear-heading-md mb-2">Campaign Performance</h3>
                          <p className="linear-body-sm" style={{ color: 'var(--linear-text-muted)' }}>
                            Real-time insights across all advertising channels
                          </p>
                        </div>

                        {/* Stats Cards */}
                        <div className="grid grid-cols-3 gap-4">
                          <div className="linear-card">
                            <div className="linear-body-sm mb-1" style={{ color: 'var(--linear-text-muted)' }}>Total Reach</div>
                            <div className="linear-heading-md" style={{ color: 'var(--linear-text-primary)' }}>2.4M</div>
                            <div className="linear-body-sm text-green-400">+12% vs last month</div>
                          </div>
                          <div className="linear-card">
                            <div className="linear-body-sm mb-1" style={{ color: 'var(--linear-text-muted)' }}>Active Campaigns</div>
                            <div className="linear-heading-md" style={{ color: 'var(--linear-text-primary)' }}>47</div>
                            <div className="linear-body-sm text-blue-400">+5 this week</div>
                          </div>
                          <div className="linear-card">
                            <div className="linear-body-sm mb-1" style={{ color: 'var(--linear-text-muted)' }}>ROI</div>
                            <div className="linear-heading-md text-green-400">247%</div>
                            <div className="linear-body-sm text-green-400">+23% improvement</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
            {/* Linear-style social proof */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-8 mt-16"
            >
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" style={{ color: 'var(--linear-text-secondary)' }} />
                <span className="linear-body-sm">10M+ ad options</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" style={{ color: 'var(--linear-text-secondary)' }} />
                <span className="linear-body-sm">Real-time availability</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" style={{ color: 'var(--linear-text-secondary)' }} />
                <span className="linear-body-sm">Free to use</span>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Stats section - Linear style */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
        transition={{ duration: 0.8, delay: 0.8 }}
        className="py-16"
        style={{
          borderTop: '1px solid var(--linear-border)',
          background: 'var(--linear-bg-secondary)'
        }}
      >
        <div className="content-container">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="linear-heading-lg mb-2">10,000+</div>
              <div className="linear-body-sm">Active Users</div>
            </div>
            <div>
              <div className="linear-heading-lg mb-2">99.9%</div>
              <div className="linear-body-sm">Uptime</div>
            </div>
            <div>
              <div className="linear-heading-lg mb-2">150%</div>
              <div className="linear-body-sm">ROI Increase</div>
            </div>
            <div>
              <div className="linear-heading-lg mb-2">24/7</div>
              <div className="linear-body-sm">Support</div>
            </div>
          </div>
        </div>
      </motion.div>
    </section>
  );
}

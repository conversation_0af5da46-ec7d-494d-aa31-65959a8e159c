# ADmyBRAND AI Suite - Landing Page

A modern, professional landing page for ADmyBRAND AI Suite, built with Next.js and inspired by Linear and Notion's clean design aesthetics.

## 🚀 Features

- **Professional Design**: Clean, minimal aesthetic inspired by Linear and Notion
- **Smooth Scrolling**: Optimized scroll performance with hardware acceleration
- **Responsive Design**: Mobile-first approach with perfect tablet and desktop layouts
- **Performance Optimized**: Lazy loading, code splitting, and performance monitoring
- **Accessibility**: WCAG compliant with reduced motion support
- **Modern Stack**: Next.js 13.5+, TypeScript, Tailwind CSS, Framer Motion

## 📋 Requirements

- **Node.js**: Version 14.0.0 or higher
- **npm**: Version 6.0.0 or higher (comes with Node.js)

## 🛠️ Installation

1. **Check Node.js version** (must be 14+):
   ```bash
   node --version
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Open in browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
├── app/                    # Next.js 13+ App Router
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Home page
├── components/
│   ├── layout/            # Layout components (navigation, footer)
│   ├── sections/          # Page sections (hero, features, pricing)
│   └── ui/                # Reusable UI components
├── lib/                   # Utility functions and helpers
└── public/                # Static assets
```

## 🎨 Design System

The project uses a professional design system inspired by Linear and Notion:

- **Colors**: Clean grays, whites, and subtle blues
- **Typography**: Inter font with optimized spacing
- **Spacing**: Consistent 6rem section spacing
- **Components**: Card-based layouts with subtle shadows
- **Animations**: Smooth, performant micro-interactions

## 🚀 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Node.js Version Management

If you're using nvm (Node Version Manager):

```bash
# Use the specified Node.js version
nvm use

# Or install and use
nvm install 14
nvm use 14
```

### Environment Variables

Create a `.env.local` file for environment-specific variables:

```env
# Add your environment variables here
NEXT_PUBLIC_API_URL=your_api_url
```

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🎯 Performance

The landing page is optimized for:

- **Core Web Vitals**: Excellent LCP, FID, and CLS scores
- **Smooth Scrolling**: 60fps scroll performance
- **Fast Loading**: Lazy loading and code splitting
- **Mobile Performance**: Optimized for mobile devices

## 🤝 Contributing

1. Ensure Node.js 14+ is installed
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`
4. Make your changes
5. Test thoroughly on different devices
6. Submit a pull request

## 📄 License

This project is proprietary and confidential.

---

Built with ❤️ for modern marketing teams

import { GlassCard } from './glass-card';
import { Star } from 'lucide-react';

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating: number;
}

export function TestimonialCard({ name, role, company, content, avatar, rating }: TestimonialCardProps) {
  return (
    <GlassCard className="p-8 h-full flex flex-col">
      <div className="flex items-center mb-4">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={`h-5 w-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
          />
        ))}
      </div>
      
      <blockquote className="italic mb-6 flex-1" style={{ color: 'var(--linear-text-secondary)' }}>
        "{content}"
      </blockquote>

      <div className="flex items-center space-x-4">
        <img
          src={avatar}
          alt={name}
          className="h-12 w-12 rounded-full object-cover"
        />
        <div>
          <div className="font-semibold" style={{ color: 'var(--linear-text-primary)' }}>{name}</div>
          <div className="text-sm" style={{ color: 'var(--linear-text-muted)' }}>{role} at {company}</div>
        </div>
      </div>
    </GlassCard>
  );
}
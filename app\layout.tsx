import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from 'sonner';

// Linear/Notion-inspired font configuration
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  weight: ['300', '400', '500', '600', '700'],
  fallback: [
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'sans-serif'
  ]
});

export const metadata: Metadata = {
  metadataBase: new URL('https://admybrand.com'),
  title: 'ADmyBRAND AI Suite - Marketing Intelligence That Works',
  description: 'Transform your marketing with AI-powered insights, automated workflows, and predictive analytics. Join 10,000+ teams building better campaigns.',
  keywords: 'AI marketing, marketing automation, predictive analytics, brand intelligence, marketing tools',
  authors: [{ name: 'ADmyBRAND Team' }],
  creator: 'ADmyBRAND',
  publisher: 'ADmyBRAND',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'ADmyBRAND AI Suite - Marketing Intelligence That Works',
    description: 'Transform your marketing with AI-powered insights, automated workflows, and predictive analytics.',
    type: 'website',
    locale: 'en_US',
    url: 'https://admybrand.com',
    siteName: 'ADmyBRAND AI Suite',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'ADmyBRAND AI Suite - Marketing Intelligence Platform'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ADmyBRAND AI Suite - Marketing Intelligence That Works',
    description: 'Transform your marketing with AI-powered insights, automated workflows, and predictive analytics.',
    images: ['/og-image.jpg'],
    creator: '@admybrand',
    site: '@admybrand'
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  alternates: {
    canonical: 'https://admybrand.com',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} scroll-smooth`} style={{ scrollBehavior: 'smooth' }}>
      <head>
        {/* Performance Hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* Viewport and Theme */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="theme-color" content="#0D1117" />
        <meta name="color-scheme" content="dark" />

        {/* Performance and Security */}
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="format-detection" content="telephone=no" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${inter.className} antialiased`} style={{ background: 'var(--linear-bg-primary)', color: 'var(--linear-text-primary)' }}>
        {children}
        <Toaster
          position="top-right"
          toastOptions={{
            style: {
              background: 'var(--linear-bg-secondary)',
              backdropFilter: 'blur(20px)',
              border: '1px solid var(--linear-border)',
              borderRadius: '8px',
              color: 'var(--linear-text-primary)'
            }
          }}
        />
      </body>
    </html>
  );
}
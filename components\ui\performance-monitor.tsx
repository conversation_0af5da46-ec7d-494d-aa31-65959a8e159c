'use client';

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
}

export function PerformanceMonitor({ 
  enabled = process.env.NODE_ENV === 'development',
  onMetrics,
}: {
  enabled?: boolean;
  onMetrics?: (metrics: PerformanceMetrics) => void;
}) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null,
  });

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    const updateMetrics = (newMetrics: Partial<PerformanceMetrics>) => {
      setMetrics(prev => {
        const updated = { ...prev, ...newMetrics };
        onMetrics?.(updated);
        return updated;
      });
    };

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
      if (fcpEntry) {
        updateMetrics({ fcp: fcpEntry.startTime });
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        updateMetrics({ ttfb: navigationEntry.responseStart - navigationEntry.requestStart });
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            updateMetrics({ lcp: lastEntry.startTime });
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              updateMetrics({ fid: entry.processingStart - entry.startTime });
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });

          // Cumulative Layout Shift
          let clsValue = 0;
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
                updateMetrics({ cls: clsValue });
              }
            });
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          return () => {
            lcpObserver.disconnect();
            fidObserver.disconnect();
            clsObserver.disconnect();
          };
        } catch (error) {
          console.warn('Performance Observer not supported:', error);
        }
      }
    };

    // Wait for page load
    if (document.readyState === 'complete') {
      measureWebVitals();
    } else {
      window.addEventListener('load', measureWebVitals);
      return () => window.removeEventListener('load', measureWebVitals);
    }
  }, [enabled, onMetrics]);

  if (!enabled) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-xs">
      <h3 className="font-bold mb-2">Performance Metrics</h3>
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>FCP:</span>
          <span className={getScoreColor(metrics.fcp, 1800, 3000)}>
            {metrics.fcp ? `${Math.round(metrics.fcp)}ms` : 'Measuring...'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>LCP:</span>
          <span className={getScoreColor(metrics.lcp, 2500, 4000)}>
            {metrics.lcp ? `${Math.round(metrics.lcp)}ms` : 'Measuring...'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>FID:</span>
          <span className={getScoreColor(metrics.fid, 100, 300)}>
            {metrics.fid ? `${Math.round(metrics.fid)}ms` : 'Measuring...'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>CLS:</span>
          <span className={getScoreColor(metrics.cls, 0.1, 0.25, true)}>
            {metrics.cls ? metrics.cls.toFixed(3) : 'Measuring...'}
          </span>
        </div>
        <div className="flex justify-between">
          <span>TTFB:</span>
          <span className={getScoreColor(metrics.ttfb, 800, 1800)}>
            {metrics.ttfb ? `${Math.round(metrics.ttfb)}ms` : 'Measuring...'}
          </span>
        </div>
      </div>
    </div>
  );
}

function getScoreColor(
  value: number | null, 
  goodThreshold: number, 
  poorThreshold: number,
  lowerIsBetter: boolean = false
): string {
  if (value === null) return 'text-gray-400';
  
  if (lowerIsBetter) {
    if (value <= goodThreshold) return 'text-green-400';
    if (value <= poorThreshold) return 'text-yellow-400';
    return 'text-red-400';
  } else {
    if (value <= goodThreshold) return 'text-green-400';
    if (value <= poorThreshold) return 'text-yellow-400';
    return 'text-red-400';
  }
}

// Resource timing monitor
export function ResourceMonitor({ enabled = false }: { enabled?: boolean }) {
  const [resources, setResources] = useState<PerformanceResourceTiming[]>([]);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    const updateResources = () => {
      const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      setResources(resourceEntries);
    };

    updateResources();

    const observer = new PerformanceObserver((list) => {
      updateResources();
    });

    observer.observe({ entryTypes: ['resource'] });

    return () => observer.disconnect();
  }, [enabled]);

  if (!enabled) return null;

  const slowResources = resources
    .filter(resource => resource.duration > 1000)
    .sort((a, b) => b.duration - a.duration)
    .slice(0, 5);

  return (
    <div className="fixed bottom-4 left-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-sm">
      <h3 className="font-bold mb-2">Slow Resources</h3>
      {slowResources.length === 0 ? (
        <p className="text-green-400">All resources loading efficiently!</p>
      ) : (
        <div className="space-y-1">
          {slowResources.map((resource, index) => (
            <div key={index} className="flex justify-between">
              <span className="truncate mr-2">
                {resource.name.split('/').pop()}
              </span>
              <span className="text-red-400">
                {Math.round(resource.duration)}ms
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Memory usage monitor
export function MemoryMonitor({ enabled = false }: { enabled?: boolean }) {
  const [memoryInfo, setMemoryInfo] = useState<any>(null);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory);
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000);

    return () => clearInterval(interval);
  }, [enabled]);

  if (!enabled || !memoryInfo) return null;

  const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1048576);
  const totalMB = Math.round(memoryInfo.totalJSHeapSize / 1048576);
  const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1048576);

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50">
      <h3 className="font-bold mb-2">Memory Usage</h3>
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>Used:</span>
          <span className={usedMB > totalMB * 0.8 ? 'text-red-400' : 'text-green-400'}>
            {usedMB} MB
          </span>
        </div>
        <div className="flex justify-between">
          <span>Total:</span>
          <span>{totalMB} MB</span>
        </div>
        <div className="flex justify-between">
          <span>Limit:</span>
          <span>{limitMB} MB</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
          <div
            className={`h-2 rounded-full ${
              usedMB / limitMB > 0.8 ? 'bg-red-400' : 'bg-green-400'
            }`}
            style={{ width: `${(usedMB / limitMB) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}

// Bundle analyzer component
export function BundleAnalyzer({ enabled = false }: { enabled?: boolean }) {
  const [bundleSize, setBundleSize] = useState<number | null>(null);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    // Estimate bundle size from resource timing
    const estimateBundleSize = () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(r => r.name.includes('.js'));
      const totalSize = jsResources.reduce((sum, resource) => {
        return sum + (resource.transferSize || 0);
      }, 0);
      setBundleSize(totalSize);
    };

    setTimeout(estimateBundleSize, 2000);
  }, [enabled]);

  if (!enabled || bundleSize === null) return null;

  const sizeMB = (bundleSize / 1048576).toFixed(2);

  return (
    <div className="fixed top-4 left-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50">
      <h3 className="font-bold mb-2">Bundle Analysis</h3>
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>JS Bundle Size:</span>
          <span className={parseFloat(sizeMB) > 1 ? 'text-yellow-400' : 'text-green-400'}>
            {sizeMB} MB
          </span>
        </div>
        {parseFloat(sizeMB) > 1 && (
          <p className="text-yellow-400 text-xs mt-2">
            Consider code splitting for better performance
          </p>
        )}
      </div>
    </div>
  );
}

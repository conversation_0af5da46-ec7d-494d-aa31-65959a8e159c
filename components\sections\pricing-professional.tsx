'use client';

import { motion } from 'framer-motion';
import { useState, useRef } from 'react';
import { Check, Star, ArrowR<PERSON>, Zap } from 'lucide-react';

const plans = [
  {
    name: 'Advertiser',
    price: 'Free',
    description: 'Perfect for businesses looking to advertise across India',
    features: [
      'Search 10M+ ad spaces',
      'Real-time availability',
      'Campaign planning tools',
      'Basic analytics',
      'Email support',
      'Mobile app access'
    ],
    cta: 'Start Advertising',
    popular: false
  },
  {
    name: 'Agency Partner',
    price: 'Commission',
    description: 'Advanced tools for advertising agencies and resellers',
    features: [
      'White-label portals',
      'Client management',
      'Advanced analytics',
      'Priority support',
      'Custom branding',
      'Revenue sharing',
      'Dedicated account manager',
      'API access'
    ],
    cta: 'Become Partner',
    popular: true
  },
  {
    name: 'Media Owner',
    price: 'Revenue Share',
    description: 'Complete solution for media space owners and sellers',
    features: [
      'Inventory management',
      'Automated booking',
      'Performance tracking',
      'Payment processing',
      'Custom pricing',
      'Analytics dashboard',
      'Multi-channel listing',
      'Training & onboarding'
    ],
    cta: 'List Your Space',
    popular: false
  }
];

const faqs = [
  {
    question: 'How does ADmyBRAND work?',
    answer: 'ADmyBRAND is a marketplace connecting advertisers with media owners. Search, compare, and book advertising spaces across India in just a few clicks.'
  },
  {
    question: 'Is it free to use?',
    answer: 'Yes, searching and booking ad spaces is completely free for advertisers. We earn through commissions from successful bookings.'
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards, UPI, net banking, and digital wallets. Payments are secure and processed instantly.'
  },
  {
    question: 'Do you offer refunds?',
    answer: 'Yes, we offer a 30-day money-back guarantee for all paid plans.'
  }
];

export default function PricingProfessional() {
  const [hoveredPlan, setHoveredPlan] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  return (
    <section ref={sectionRef} className="section-spacing scroll-optimized transform-gpu will-change-transform" style={{ background: 'var(--linear-bg-primary)' }}>
      <div className="content-container">
        
        {/* Section header */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="linear-badge purple mb-6"
          >
            <Zap className="w-4 h-4" />
            Simple Pricing
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="linear-heading-xl mb-6"
          >
            Choose the perfect plan
            <br />
            <span className="linear-gradient-text">for your advertising needs</span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Start with a 14-day free trial. No credit card required. 
            Upgrade or downgrade at any time.
          </motion.p>
        </div>

        {/* Pricing cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative linear-card rounded-2xl p-8 ${
                plan.popular
                  ? 'border-2 shadow-xl'
                  : 'shadow-lg'
              }`}
              style={{
                borderColor: plan.popular ? 'var(--linear-purple)' : 'var(--linear-border)'
              }}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="btn-linear-primary px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
                    <Star className="w-4 h-4" />
                    Most Popular
                  </div>
                </div>
              )}
              
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                
                <div className="mb-6">
                  <span className="text-5xl font-bold text-gray-900">${plan.price}</span>
                  <span className="text-gray-600 ml-2">/month</span>
                </div>
                
                <button 
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-all ${
                    plan.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                >
                  {plan.cta}
                  {plan.cta !== 'Contact Sales' && <ArrowRight className="w-4 h-4 ml-2 inline" />}
                </button>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 mb-4">Everything included:</h4>
                {plan.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-600">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="max-w-3xl mx-auto"
        >
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Frequently asked questions
          </h3>
          
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 border border-gray-200"
              >
                <h4 className="font-semibold text-gray-900 mb-3">{faq.question}</h4>
                <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="bg-white rounded-2xl p-12 border border-gray-200 shadow-lg">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to transform your marketing?
            </h3>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Join 10,000+ teams who trust ADmyBRAND to power their marketing success.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary-linear flex items-center gap-2 px-8 py-4 text-base">
                Start Free Trial
                <ArrowRight className="w-5 h-5" />
              </button>
              
              <button className="btn-secondary-linear px-8 py-4 text-base">
                Schedule Demo
              </button>
            </div>
            
            <p className="text-sm text-gray-500 mt-6">
              No credit card required • 14-day free trial • Cancel anytime
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Immediate dark theme application - prevents white flash */
* {
  background-color: inherit;
}

html, body {
  background-color: #0D1117 !important;
  color: #F0F6FC !important;
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.75rem;

    /* Enhanced Brand Colors */
    --brand-primary: 262 83% 58%;
    --brand-secondary: 330 81% 60%;
    --brand-accent: 196 100% 47%;

    /* Enhanced Semantic Colors */
    --success: 142 71% 45%;
    --warning: 38 92% 50%;
    --error: 0 84% 60%;
    --info: 221 83% 53%;

    /* True Linear/Notion Color System */
    --gray-50: 250 250 250;    /* Linear's lightest gray */
    --gray-100: 245 245 245;   /* Notion's card backgrounds */
    --gray-200: 229 229 229;   /* Linear's border color */
    --gray-300: 212 212 212;   /* Subtle borders */
    --gray-400: 163 163 163;   /* Muted text */
    --gray-500: 115 115 115;   /* Secondary text */
    --gray-600: 82 82 82;      /* Primary text light */
    --gray-700: 64 64 64;      /* Primary text */
    --gray-800: 38 38 38;      /* Linear's dark text */
    --gray-900: 23 23 23;      /* True black text */

    /* Linear's exact accent colors */
    --linear-purple: 92 84 178;    /* Linear's signature purple */
    --linear-blue: 59 130 246;     /* Linear's blue accent */
    --notion-blue: 35 131 226;     /* Notion's blue */
    --accent-primary: 92 84 178;   /* Primary accent */
    --accent-hover: 76 70 149;     /* Hover state */

    /* Notion-inspired semantic colors */
    --success: 34 197 94;
    --warning: 245 158 11;
    --error: 239 68 68;

    /* Professional spacing scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Typography scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-blur: 20px;
  }
  
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
    /* Linear/Notion-inspired smooth rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
    /* Clean scrolling like Linear */
    -webkit-overflow-scrolling: touch;
  }

  html {
    /* Linear's dark theme colors applied to root */
    background: var(--linear-bg-primary) !important;
    color: var(--linear-text-primary) !important;
  }

  body {
    @apply font-sans antialiased;
    /* Linear's exact Inter font configuration */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "calt", "liga";
    font-variant-numeric: tabular-nums;

    /* Linear's exact typography specs */
    line-height: 1.6;                 /* Linear's line height */
    letter-spacing: -0.014em;         /* Linear's exact letter spacing */
    font-weight: 400;                 /* Linear's base font weight */

    /* Linear's dark theme colors - forced */
    background: var(--linear-bg-primary) !important;
    color: var(--linear-text-primary) !important;

    /* Linear's text rendering optimization */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Smooth scrolling like Linear */
    scroll-behavior: smooth;
  }
}

/* 2025 Design System - Enhanced Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.glass-subtle {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.06);
}

/* 2025 Enhanced Gradients */
.gradient-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #ec4899 0%, #f43f5e 50%, #ef4444 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 50%, #3b82f6 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-mesh {
  background:
    radial-gradient(at 40% 20%, hsla(262, 83%, 70%, 0.8) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(196, 100%, 60%, 0.8) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 95%, 0.9) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(330, 81%, 70%, 0.8) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 80%, 0.8) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(262, 83%, 75%, 0.8) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 80%, 0.8) 0px, transparent 50%);
}

.gradient-mesh-dark {
  background:
    radial-gradient(at 40% 20%, hsla(262, 83%, 30%, 0.4) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(196, 100%, 30%, 0.4) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 15%, 0.5) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(330, 81%, 30%, 0.4) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 30%, 0.4) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(262, 83%, 35%, 0.4) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 30%, 0.4) 0px, transparent 50%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Typography Scale */
.text-display-2xl {
  font-size: clamp(3rem, 8vw, 6rem);
  line-height: 1.1;
  letter-spacing: -0.04em;
  font-weight: 800;
}

.text-display-xl {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  line-height: 1.1;
  letter-spacing: -0.03em;
  font-weight: 700;
}

.text-display-lg {
  font-size: clamp(2rem, 4vw, 3.5rem);
  line-height: 1.2;
  letter-spacing: -0.02em;
  font-weight: 700;
}

.text-heading-xl {
  font-size: clamp(1.75rem, 3vw, 2.5rem);
  line-height: 1.2;
  letter-spacing: -0.01em;
  font-weight: 600;
}

.text-heading-lg {
  font-size: clamp(1.5rem, 2.5vw, 2rem);
  line-height: 1.3;
  letter-spacing: -0.01em;
  font-weight: 600;
}

.text-body-xl {
  font-size: 1.25rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-body-lg {
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-body {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-caption {
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 500;
}

/* Linear/Notion-inspired Professional Styles */

/* Linear's exact section spacing */
.section-spacing {
  padding: 5rem 0;  /* Linear uses 80px (5rem) sections */
}

.section-spacing-sm {
  padding: 3rem 0;  /* Linear's small sections */
}

.section-spacing-lg {
  padding: 7rem 0;  /* Linear's large sections */
}

/* Linear's exact content containers */
.content-container {
  max-width: 1120px;  /* Linear's exact max-width */
  margin: 0 auto;
  padding: 0 1.5rem;
}

.content-container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Linear-inspired subtle borders */
.border-subtle {
  border: 1px solid rgb(229 231 235);
}

.border-subtle-top {
  border-top: 1px solid rgb(229 231 235);
}

.border-subtle-bottom {
  border-bottom: 1px solid rgb(229 231 235);
}

/* Linear/Notion card styles with micro-interactions */
.card-professional {
  background: #ffffff;
  border: 1px solid rgb(229 229 229);  /* Linear's exact border */
  border-radius: 8px;  /* Linear's radius */
  padding: 1.5rem;  /* Slightly tighter like Linear */
  box-shadow: none;  /* Linear uses minimal shadows */
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);  /* Hardware acceleration */
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.card-professional:hover {
  border-color: rgb(212 212 212);  /* Linear's hover border */
  transform: translateY(-2px) translateZ(0);  /* Gentle lift */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);  /* Notion-style shadow */
}

.card-professional:active {
  transform: translateY(-1px) scale(0.995) translateZ(0);
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Subtle shine effect on hover */
.card-professional::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.card-professional:hover::before {
  left: 100%;
}

/* Linear's exact button styles with micro-interactions */
.btn-primary-linear {
  background: rgb(23 23 23);  /* Linear's exact black */
  color: white;
  border: none;
  border-radius: 6px;  /* Linear uses 6px radius */
  padding: 8px 16px;  /* Linear's exact padding */
  font-weight: 500;
  font-size: 14px;  /* Linear's exact font size */
  line-height: 20px;  /* Linear's line height */
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);  /* Linear's easing */
  cursor: pointer;
  letter-spacing: -0.011em;  /* Linear's letter spacing */
  position: relative;
  overflow: hidden;
  transform: translateZ(0);  /* Hardware acceleration */
}

.btn-primary-linear:hover {
  background: rgb(38 38 38);  /* Linear's hover state */
  transform: translateY(-1px) translateZ(0);  /* Subtle lift */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* Gentle shadow */
}

.btn-primary-linear:active {
  transform: translateY(0) scale(0.98) translateZ(0);  /* Click feedback */
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ripple effect for buttons */
.btn-primary-linear::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.btn-primary-linear:active::before {
  width: 300px;
  height: 300px;
}

.btn-secondary-linear {
  background: transparent;
  color: rgb(82 82 82);  /* Linear's secondary text */
  border: 1px solid rgb(229 229 229);  /* Linear's border */
  border-radius: 6px;  /* Linear's radius */
  padding: 8px 16px;  /* Linear's padding */
  font-weight: 500;
  font-size: 14px;  /* Linear's font size */
  line-height: 20px;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  letter-spacing: -0.011em;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.btn-secondary-linear:hover {
  border-color: rgb(212 212 212);  /* Linear's hover border */
  background: rgb(250 250 250);  /* Linear's hover background */
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.btn-secondary-linear:active {
  transform: translateY(0) scale(0.98) translateZ(0);
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Linear/Notion-style micro-interactions */

/* Clickable elements with feedback */
.interactive-element {
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px) translateZ(0);
}

.interactive-element:active {
  transform: translateY(0) scale(0.98) translateZ(0);
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Notion-style page transitions */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Linear-style loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Pulse animation for loading dots */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgb(163 163 163);
  animation: pulse 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Smooth focus states */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  transition: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover glow effect for special elements */
.hover-glow {
  position: relative;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  opacity: 0;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.hover-glow:hover::before {
  opacity: 1;
}

/* Linear-consistent form styles */
input, textarea, select {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.011em;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: rgb(59 130 246);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Linear-style badges and pills */
.badge-linear {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  background: rgb(243 244 246);
  border: 1px solid rgb(229 229 229);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(75 85 99);
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.badge-linear:hover {
  background: rgb(249 250 251);
  border-color: rgb(212 212 212);
}

/* Linear-style dividers */
.divider-linear {
  height: 1px;
  background: rgb(229 229 229);
  border: none;
  margin: 2rem 0;
}

/* Linear-style text hierarchy */
.text-display-2xl {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: rgb(23 23 23);
}

.text-display-xl {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: rgb(23 23 23);
}

.text-display-lg {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.015em;
  color: rgb(23 23 23);
}

.text-body-lg {
  font-size: 1.125rem;
  line-height: 1.5;
  letter-spacing: -0.011em;
  color: rgb(82 82 82);
}

.text-body {
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: -0.011em;
  color: rgb(82 82 82);
}

.text-body-sm {
  font-size: 0.875rem;
  line-height: 1.4;
  letter-spacing: -0.006em;
  color: rgb(115 115 115);
}

/* Linear-style status indicators */
.status-online {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgb(34 197 94);
}

.status-online::before {
  content: '';
  width: 8px;
  height: 8px;
  background: rgb(34 197 94);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Linear-style code blocks */
.code-linear {
  background: rgb(249 250 251);
  border: 1px solid rgb(229 229 229);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  color: rgb(75 85 99);
}

/* Linear-style tooltips */
.tooltip-linear {
  position: relative;
  display: inline-block;
}

.tooltip-linear::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgb(23 23 23);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 50;
}

.tooltip-linear:hover::after {
  opacity: 1;
}

/* Linear-consistent spacing system */
.section-spacing {
  padding: 6rem 0;
}

@media (max-width: 768px) {
  .section-spacing {
    padding: 4rem 0;
  }
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media (max-width: 640px) {
  .content-container {
    padding: 0 1rem;
  }
}

/* Linear-style grid layouts */
.grid-linear-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.grid-linear-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

@media (max-width: 1024px) {
  .grid-linear-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .grid-linear-2,
  .grid-linear-3 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Linear-style animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

/* Linear-style selection */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(23 23 23);
}

/* Linear-style scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgb(203 213 225);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(148 163 184);
}

/* Linear-style focus management */
.focus-visible {
  outline: 2px solid rgb(59 130 246);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Linear-inspired navigation */
.nav-professional {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgb(229 231 235);
}

/* Notion-inspired content blocks */
.content-block {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid rgb(229 231 235);
  margin-bottom: 1.5rem;
}

/* Professional shadows */
.shadow-professional {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-professional-lg {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Linear-inspired focus states */
.focus-professional:focus {
  outline: none;
  ring: 2px solid rgb(59 130 246);
  ring-offset: 2px;
}

/* Smooth page transitions */
.page-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional text colors */
.text-primary {
  color: rgb(17 24 39);
}

.text-secondary {
  color: rgb(75 85 99);
}

.text-muted {
  color: rgb(107 114 128);
}

/* Clean spacing utilities */
.space-section {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.space-component {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

/* Professional animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .section-spacing {
    padding: 4rem 0;
  }

  .content-container {
    padding: 0 1rem;
  }

  .text-display-2xl {
    font-size: 2.5rem;
  }

  .text-display-xl {
    font-size: 2rem;
  }
}

.text-overline {
  font-size: 0.75rem;
  line-height: 1.4;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Advanced 2025 Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-12px) rotate(1deg); }
  66% { transform: translateY(6px) rotate(-1deg); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.5);
    transform: scale(1.02);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes glow-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(99, 102, 241, 0.6));
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotate-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

.animate-shimmer {
  position: relative;
  overflow: hidden;
}

.animate-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

.animate-slide-up {
  animation: slide-up 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scale-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

/* Enhanced Interactive Elements */
.interactive-card {
  @apply transition-all duration-500 ease-out;
  transform: translateY(0px) scale(1);
  will-change: transform, box-shadow;
}

.interactive-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 40px rgba(99, 102, 241, 0.1);
}

.interactive-button {
  @apply relative overflow-hidden transition-all duration-300;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  will-change: transform, box-shadow;
}

.interactive-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
  transition: left 0.6s ease;
}

.interactive-button:hover::before {
  left: 100%;
}

.interactive-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 16px 32px rgba(99, 102, 241, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.interactive-button:active {
  transform: translateY(-1px) scale(0.98);
}

.hover-lift {
  @apply transition-all duration-300 ease-out;
  will-change: transform;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-glow {
  @apply transition-all duration-300 ease-out;
  will-change: filter;
}

.hover-glow:hover {
  filter: drop-shadow(0 8px 16px rgba(99, 102, 241, 0.3));
}

/* Glassmorphism Components */
.glass-card {
  @apply backdrop-blur-xl bg-white/10 border border-white/20 rounded-2xl;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-nav {
  @apply backdrop-blur-md bg-white/80 border-b border-white/20;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Enhanced Modern Shadows */
.shadow-soft {
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow-medium {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-large {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-2xl {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.shadow-glow {
  box-shadow:
    0 0 20px rgba(99, 102, 241, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.12);
}

.shadow-glow-lg {
  box-shadow:
    0 0 40px rgba(99, 102, 241, 0.4),
    0 16px 64px rgba(0, 0, 0, 0.15);
}

.shadow-colored {
  box-shadow:
    0 10px 15px -3px rgba(99, 102, 241, 0.1),
    0 4px 6px -2px rgba(99, 102, 241, 0.05);
}

/* Responsive Utilities */
.container-padding {
  @apply px-4 sm:px-6 lg:px-8 xl:px-12;
}

.section-padding {
  @apply py-16 lg:py-24 xl:py-32;
}

.max-width-content {
  max-width: 1400px;
}

.max-width-text {
  max-width: 65ch;
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Enhanced Smooth Scrolling & Performance */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
  height: 100%;
}

body {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Optimize elements for smooth scrolling */
section, div, main {
  /* GPU acceleration for smooth scrolling */
  will-change: auto;
}

/* Optimize animations during scroll */
.animate-float-slow,
.animate-glow-pulse,
.animate-gradient,
.animate-morphing-blob,
.animate-particle-float,
.animate-liquid-morph {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  /* Reduce animation complexity during scroll */
  animation-fill-mode: both;
}

/* Smooth scroll for anchor links */
a[href^="#"] {
  scroll-behavior: smooth;
}

/* Optimize fixed elements */
.fixed, .sticky {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth scroll reveal animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  will-change: opacity, transform;
}

.scroll-reveal.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger animation for child elements */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
  will-change: opacity, transform;
}

.stagger-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Scroll progress indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--scroll-progress, 0%);
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
  z-index: 9999;
  transition: width 0.1s ease-out;
}

/* Performance optimizations for scroll */
.will-change-scroll {
  will-change: scroll-position;
}

/* Smooth transitions for interactive elements */
button, a, .interactive {
  transition: all 0.2s ease-out;
  will-change: transform, opacity;
}

button:hover, a:hover, .interactive:hover {
  transform: translateY(-1px);
}

/* Optimize hover states */
@media (hover: hover) {
  .hover-lift:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-out;
  }

  .hover-scale:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease-out;
  }
}

/* Reduce motion for scroll animations */
@media (prefers-reduced-motion: reduce) {
  .scroll-reveal,
  .stagger-item {
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
  }

  .scroll-progress {
    display: none !important;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* 2025 Modern Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

.bg-noise {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.border-gradient {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #6366f1, #8b5cf6) border-box;
}

.backdrop-blur-xs {
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
  -webkit-backdrop-filter: blur(64px);
}

.perspective-1000 {
  perspective: 1000px;
}

.transform-3d {
  transform-style: preserve-3d;
}

.rotate-x-12 {
  transform: rotateX(12deg);
}

.rotate-y-12 {
  transform: rotateY(12deg);
}

/* Enhanced Focus States */
.focus-ring-modern {
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:ring-offset-2 focus:ring-offset-white;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500/50;
}

/* Advanced 2025 Animations */
@keyframes magnetic-hover {
  0% { transform: translate(0, 0) scale(1); }
  100% { transform: translate(var(--mouse-x, 0), var(--mouse-y, 0)) scale(1.05); }
}

@keyframes text-reveal {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes morphing-blob {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    transform: translate(10px, -10px) rotate(180deg);
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.5;
  }
}

@keyframes liquid-morph {
  0%, 100% {
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
  }
  14% {
    border-radius: 40% 60% 54% 46% / 49% 60% 40% 51%;
  }
  28% {
    border-radius: 54% 46% 38% 62% / 49% 70% 30% 51%;
  }
  42% {
    border-radius: 61% 39% 55% 45% / 61% 38% 62% 39%;
  }
  56% {
    border-radius: 61% 39% 67% 33% / 70% 50% 50% 30%;
  }
  70% {
    border-radius: 50% 50% 34% 66% / 56% 68% 32% 44%;
  }
  84% {
    border-radius: 46% 54% 50% 50% / 35% 61% 39% 65%;
  }
}

@keyframes typing-cursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes elastic-bounce {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.1, 0.9);
  }
  40% {
    transform: scale(0.9, 1.1);
  }
  60% {
    transform: scale(1.05, 0.95);
  }
  80% {
    transform: scale(0.95, 1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Advanced Animation Classes */
.animate-magnetic {
  animation: magnetic-hover 0.3s ease-out forwards;
}

.animate-text-reveal {
  animation: text-reveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-morphing-blob {
  animation: morphing-blob 8s ease-in-out infinite;
}

.animate-particle-float {
  animation: particle-float 3s ease-in-out infinite;
}

.animate-liquid-morph {
  animation: liquid-morph 10s ease-in-out infinite;
}

.animate-typing-cursor {
  animation: typing-cursor 1s infinite;
}

.animate-elastic-bounce {
  animation: elastic-bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Stagger Animation Utilities */
.stagger-child {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stagger-child.revealed {
  opacity: 1;
  transform: translateY(0);
}

.stagger-child:nth-child(1) { transition-delay: 0.1s; }
.stagger-child:nth-child(2) { transition-delay: 0.2s; }
.stagger-child:nth-child(3) { transition-delay: 0.3s; }
.stagger-child:nth-child(4) { transition-delay: 0.4s; }
.stagger-child:nth-child(5) { transition-delay: 0.5s; }
.stagger-child:nth-child(6) { transition-delay: 0.6s; }

/* Advanced Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-tilt {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-tilt:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
}

.hover-glow-intense {
  transition: all 0.3s ease;
}

.hover-glow-intense:hover {
  filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.6));
  transform: scale(1.05);
}

/* Accessibility Improvements */

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus Visible */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-subtle {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #000;
  }

  .text-gray-600 {
    color: #000;
  }

  .text-gray-700 {
    color: #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable parallax and transform animations */
  .animate-float-slow,
  .animate-glow-pulse,
  .animate-gradient,
  .animate-morphing-blob,
  .animate-particle-float,
  .animate-liquid-morph {
    animation: none !important;
  }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  .glass-subtle {
    background: white !important;
    border: 1px solid #ccc !important;
  }
}

/* ===== SCROLL PERFORMANCE OPTIMIZATIONS ===== */

/* Hardware acceleration for smooth scrolling */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Optimize scroll performance */
* {
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}

/* Optimized animations for scroll-triggered elements */
.scroll-optimized {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth scroll reveal animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* Stagger animation children */
.stagger-child {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
}

.stagger-child.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Optimized hover effects */
.hover-optimized {
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.hover-optimized:hover {
  transform: translateY(-2px) translateZ(0);
}

/* Performance-optimized cards */
.card-optimized {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.card-optimized:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Scroll progress indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  transform-origin: 0%;
  z-index: 1000;
  will-change: transform;
}

/* Optimized section spacing */
.section-spacing {
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

@media (max-width: 768px) {
  .section-spacing {
    padding: 4rem 0;
  }
}

/* Content container with consistent spacing */
.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

@media (max-width: 640px) {
  .content-container {
    padding: 0 1rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .scroll-reveal,
  .scroll-reveal-left,
  .scroll-reveal-right,
  .stagger-child {
    opacity: 1 !important;
    transform: none !important;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimized button styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow;
  transform: translateZ(0);
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, box-shadow;
  transform: translateZ(0);
  cursor: pointer;
}

.btn-secondary:hover {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background: white;
}

/* Enhanced scroll performance optimizations */
.section-spacing {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimized animations for better performance */
.fade-in-optimized {
  opacity: 0;
  transform: translateY(20px) translateZ(0);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-optimized.revealed {
  opacity: 1;
  transform: translateY(0) translateZ(0);
}

/* Optimized hover effects */
.hover-lift-optimized {
  will-change: transform;
  transform: translateZ(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift-optimized:hover {
  transform: translateY(-4px) translateZ(0);
}

/* Enhanced stagger animations */
.stagger-container .stagger-child {
  opacity: 0;
  transform: translateY(20px) translateZ(0);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.stagger-container .stagger-child.revealed {
  opacity: 1;
  transform: translateY(0) translateZ(0);
}

/* Optimized navigation styles */
.nav-optimized {
  will-change: transform, backdrop-filter;
  transform: translateZ(0);
  backface-visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced mobile performance */
@media (max-width: 768px) {
  .mobile-optimized {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Reduce animations on mobile for better performance */
  .reduce-motion-mobile {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }
}

/* Optimized glassmorphism effects */
.glass-optimized {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  will-change: backdrop-filter;
  transform: translateZ(0);
}

/* Performance-optimized gradients */
.gradient-optimized {
  background-attachment: fixed;
  will-change: transform;
  transform: translateZ(0);
}

/* Enhanced focus states for accessibility */
.focus-optimized:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  transform: translateZ(0);
}

/* Linear's Exact Design System - Dark Theme */
:root {
  /* Linear's exact dark color palette */
  --linear-bg-primary: #0D1117;        /* Linear's main dark background */
  --linear-bg-secondary: #161B22;      /* Linear's secondary dark background */
  --linear-bg-tertiary: #21262D;       /* Linear's card/panel background */
  --linear-bg-elevated: #30363D;       /* Linear's elevated surfaces */

  /* Linear's text colors */
  --linear-text-primary: #F0F6FC;      /* Linear's primary white text */
  --linear-text-secondary: #8B949E;    /* Linear's secondary gray text */
  --linear-text-muted: #6E7681;        /* Linear's muted text */
  --linear-text-inverse: #0D1117;      /* Dark text for light backgrounds */

  /* Linear's accent colors */
  --linear-purple: #8B5CF6;            /* Linear's signature purple */
  --linear-purple-hover: #A78BFA;      /* Purple hover state */
  --linear-blue: #3B82F6;              /* Linear's blue accent */
  --linear-green: #10B981;             /* Success/positive states */
  --linear-orange: #F59E0B;            /* Warning states */
  --linear-red: #EF4444;               /* Error states */

  /* Linear's borders and dividers */
  --linear-border: #30363D;            /* Linear's border color */
  --linear-border-muted: #21262D;      /* Subtle borders */
  --linear-border-focus: #8B5CF6;      /* Focus ring color */

  /* Linear's shadows and glows */
  --linear-shadow: rgba(0, 0, 0, 0.3);
  --linear-shadow-hover: rgba(0, 0, 0, 0.4);
  --linear-glow-purple: rgba(139, 92, 246, 0.3);
  --linear-glow-blue: rgba(59, 130, 246, 0.3);

  /* Consistent spacing scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Typography scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Animation timing */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
}

/* Consistent theme application */
.theme-consistent {
  color: var(--linear-text-primary);
  background-color: var(--linear-background);
  border-color: var(--linear-border);
  transition: all var(--transition-normal) var(--easing-standard);
}

/* Consistent button styles */
.btn-linear {
  background: var(--linear-primary);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  transition: all var(--transition-fast) var(--easing-standard);
  transform: translateZ(0);
  will-change: transform, background-color;
}

.btn-linear:hover {
  background: var(--linear-primary-hover);
  transform: translateY(-1px) translateZ(0);
}

.btn-linear-secondary {
  background: var(--linear-secondary);
  color: var(--linear-text-primary);
  border: 1px solid var(--linear-border);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  transition: all var(--transition-fast) var(--easing-standard);
  transform: translateZ(0);
  will-change: transform, background-color, border-color;
}

.btn-linear-secondary:hover {
  background: white;
  border-color: var(--linear-primary);
  transform: translateY(-1px) translateZ(0);
}

/* Consistent card styles */
.card-linear {
  background: var(--linear-background);
  border: 1px solid var(--linear-border-light);
  border-radius: 0.75rem;
  padding: var(--spacing-xl);
  box-shadow: 0 1px 3px var(--linear-shadow);
  transition: all var(--transition-normal) var(--easing-standard);
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

.card-linear:hover {
  box-shadow: 0 10px 25px var(--linear-shadow-hover);
  transform: translateY(-2px) translateZ(0);
}

/* Consistent text styles */
.text-linear-primary {
  color: var(--linear-text-primary);
}

.text-linear-secondary {
  color: var(--linear-text-secondary);
}

.text-linear-muted {
  color: var(--linear-text-muted);
}

/* Consistent spacing utilities */
.spacing-section {
  padding: var(--spacing-3xl) 0;
}

/* Linear's Exact Typography System */
.linear-heading-xl {
  font-size: 3.5rem;                   /* 56px - Linear's hero size */
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: var(--linear-text-primary);
}

.linear-heading-lg {
  font-size: 2.5rem;                   /* 40px - Linear's section headers */
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.018em;
  color: var(--linear-text-primary);
}

.linear-heading-md {
  font-size: 1.5rem;                   /* 24px - Linear's card titles */
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.014em;
  color: var(--linear-text-primary);
}

.linear-body-lg {
  font-size: 1.125rem;                 /* 18px - Linear's large body text */
  font-weight: 400;
  line-height: 1.6;
  color: var(--linear-text-secondary);
}

.linear-body {
  font-size: 1rem;                     /* 16px - Linear's default body */
  font-weight: 400;
  line-height: 1.6;
  color: var(--linear-text-secondary);
}

.linear-body-sm {
  font-size: 0.875rem;                 /* 14px - Linear's small text */
  font-weight: 400;
  line-height: 1.5;
  color: var(--linear-text-muted);
}

/* Linear's Exact Button System */
.btn-linear-primary {
  background: var(--linear-text-primary);
  color: var(--linear-bg-primary);
  border: 1px solid var(--linear-text-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  transform: translateZ(0);
  will-change: transform, background-color, box-shadow;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-linear-primary:hover {
  background: var(--linear-text-secondary);
  border-color: var(--linear-text-secondary);
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 4px 12px rgba(240, 246, 252, 0.2);
}

.btn-linear-secondary {
  background: var(--linear-bg-tertiary);
  color: var(--linear-text-primary);
  border: 1px solid var(--linear-border);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  transform: translateZ(0);
  will-change: transform, background-color, border-color;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-linear-secondary:hover {
  background: var(--linear-bg-elevated);
  border-color: var(--linear-text-secondary);
  transform: translateY(-1px) translateZ(0);
}

/* Consistent section backgrounds */
.bg-linear-primary {
  background-color: var(--linear-background);
}

.bg-linear-secondary {
  background-color: var(--linear-background-secondary);
}

/* Consistent borders */
.border-linear {
  border-color: var(--linear-border);
}

.border-linear-light {
  border-color: var(--linear-border-light);
}

/* Linear's Interface Components */
.linear-panel {
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
}

.linear-card {
  background: var(--linear-bg-secondary);
  border: 1px solid var(--linear-border);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

/* Enhanced Interface Elements for ADmyBRAND */
.linear-filter-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border);
  border-radius: 0.375rem;
  font-size: 0.75rem;
  color: var(--linear-text-secondary);
  transition: all 0.2s ease;
  cursor: pointer;
}

.linear-filter-chip.active {
  background: var(--linear-text-primary);
  color: var(--linear-bg-primary);
  border-color: var(--linear-text-primary);
}

.linear-filter-chip:hover {
  background: var(--linear-bg-elevated);
  border-color: var(--linear-text-secondary);
}

.linear-media-card {
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border);
  border-radius: 0.375rem;
  padding: 0.5rem;
  text-align: center;
  transition: all 0.2s ease;
}

.linear-media-card.active {
  border-color: var(--linear-text-secondary);
  background: var(--linear-bg-elevated);
}

.linear-media-card:hover {
  border-color: var(--linear-text-secondary);
  transform: translateY(-1px);
}

.linear-card:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: var(--linear-text-secondary);
}

/* Enhanced Interactive Elements */
.linear-interface-mini:hover {
  transform: translateY(-1px) translateZ(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--linear-text-secondary);
  transition: all 0.2s ease;
}

.linear-metric-mini:hover {
  background: var(--linear-bg-elevated);
  transition: all 0.2s ease;
}

/* Linear's Code/Interface Mockup Styles */
.linear-interface {
  background: var(--linear-bg-secondary);
  border: 1px solid var(--linear-border);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.linear-interface-header {
  background: var(--linear-bg-tertiary);
  border-bottom: 1px solid var(--linear-border);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.linear-interface-dots {
  display: flex;
  gap: 0.375rem;
}

.linear-interface-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.linear-interface-dot.red { background: #FF5F57; }
.linear-interface-dot.yellow { background: #FFBD2E; }
.linear-interface-dot.green { background: #28CA42; }

.linear-interface-content {
  padding: 1.5rem;
  background: var(--linear-bg-primary);
  min-height: 300px;
}

/* Linear Mini Interface Components */
.linear-interface-mini {
  background: var(--linear-bg-secondary);
  border: 1px solid var(--linear-border);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 280px;
}

.linear-interface-header-mini {
  background: var(--linear-bg-tertiary);
  border-bottom: 1px solid var(--linear-border);
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.linear-interface-dots-mini {
  display: flex;
  gap: 0.25rem;
}

.linear-interface-dot-mini {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.linear-interface-dot-mini.red { background: #ff5f57; }
.linear-interface-dot-mini.yellow { background: #ffbd2e; }
.linear-interface-dot-mini.green { background: #28ca42; }

.linear-interface-content-mini {
  padding: 1rem;
}

.linear-badge.green-mini {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.linear-progress-bar {
  background: var(--linear-bg-tertiary);
  height: 0.5rem;
  border-radius: 0.25rem;
  overflow: hidden;
}

.linear-progress-fill {
  background: var(--linear-text-secondary);
  height: 100%;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.linear-metric-mini {
  text-align: center;
}

.linear-chart-mini {
  height: 60px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding: 0.5rem 0;
}

.linear-chart-bars {
  display: flex;
  gap: 0.25rem;
  align-items: flex-end;
  height: 100%;
}

.linear-chart-bar {
  width: 0.5rem;
  background: var(--linear-text-secondary);
  border-radius: 0.125rem;
  min-height: 0.25rem;
}

.linear-media-card {
  background: var(--linear-bg-tertiary);
  border: 1px solid var(--linear-border);
  border-radius: 0.375rem;
  padding: 0.5rem;
  text-align: center;
}

.linear-timeline-mini {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
  margin-top: 0.5rem;
}

.linear-timeline-item {
  width: 1rem;
  height: 0.25rem;
  background: var(--linear-bg-tertiary);
  border-radius: 0.125rem;
}

.linear-timeline-item.active {
  background: var(--linear-text-secondary);
}

/* Linear's Gradient Text Effects */
.linear-gradient-text {
  background: linear-gradient(135deg, var(--linear-text-primary) 0%, var(--linear-text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Linear's Glow Effects */
.linear-glow-purple {
  box-shadow: 0 0 20px var(--linear-glow-purple);
}

.linear-glow-blue {
  box-shadow: 0 0 20px var(--linear-glow-blue);
}

/* Linear's Badge/Tag Styles */
.linear-badge {
  background: var(--linear-bg-tertiary);
  color: var(--linear-text-secondary);
  border: 1px solid var(--linear-border);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
}

.linear-badge.purple {
  background: rgba(240, 246, 252, 0.1);
  color: var(--linear-text-secondary);
  border-color: rgba(240, 246, 252, 0.3);
}

.linear-badge.blue {
  background: rgba(59, 130, 246, 0.1);
  color: var(--linear-blue);
  border-color: rgba(59, 130, 246, 0.3);
}

.spacing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
  .spacing-container {
    padding: 0 var(--spacing-md);
  }

  .spacing-section {
    padding: var(--spacing-2xl) 0;
  }
}
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from './card';
import { Button } from './button';
import { Badge } from './badge';

const PricingCalculator = () => {
  const [teamSize, setTeamSize] = useState(10);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const calculatePrice = (basePrice: number) => {
    const multiplier = teamSize <= 5 ? 1 : teamSize <= 25 ? 1.5 : 2;
    const price = basePrice * multiplier;
    return billingCycle === 'yearly' ? price * 0.8 : price;
  };

  const plans = [
    {
      name: 'Starter',
      basePrice: 29,
      features: ['Up to 5 team members', 'Basic AI features', 'Email support']
    },
    {
      name: 'Professional',
      basePrice: 99,
      features: ['Up to 25 team members', 'Advanced AI features', 'Priority support'],
      popular: true
    },
    {
      name: 'Enterprise',
      basePrice: 299,
      features: ['Unlimited team members', 'Custom AI models', 'Dedicated support']
    }
  ];

  return (
    <Card variant="glass" className="p-8">
      <div className="text-center mb-8">
        <h3 className="text-heading-lg text-gray-900 mb-4">
          Interactive Pricing Calculator
        </h3>
        <p className="text-body text-gray-600">
          Customize your plan based on team size and billing preferences
        </p>
      </div>

      {/* Controls */}
      <div className="space-y-6 mb-8">
        {/* Team Size Slider */}
        <div>
          <label className="block text-caption font-medium text-gray-700 mb-3">
            Team Size: {teamSize} members
          </label>
          <input
            type="range"
            min="1"
            max="100"
            value={teamSize}
            onChange={(e) => setTeamSize(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
        </div>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4">
          <span className={`text-body ${billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'}`}>
            Monthly
          </span>
          <button
            onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              billingCycle === 'yearly' ? 'bg-blue-600' : 'bg-gray-300'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
          <span className={`text-body ${billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'}`}>
            Yearly
          </span>
          {billingCycle === 'yearly' && (
            <Badge variant="success" size="sm">20% off</Badge>
          )}
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-6">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.name}
            className={`relative p-6 rounded-xl border-2 transition-all duration-300 ${
              plan.popular 
                ? 'border-blue-500 bg-blue-50/50' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            whileHover={{ scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge variant="info" size="sm">Most Popular</Badge>
              </div>
            )}

            <div className="text-center mb-6">
              <h4 className="text-heading-lg text-gray-900 mb-2">{plan.name}</h4>
              <div className="text-display-lg text-gray-900">
                ${Math.round(calculatePrice(plan.basePrice))}
                <span className="text-body text-gray-500">
                  /{billingCycle === 'monthly' ? 'mo' : 'yr'}
                </span>
              </div>
            </div>

            <ul className="space-y-3 mb-6">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-center text-body text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3" />
                  {feature}
                </li>
              ))}
            </ul>

            <Button 
              variant={plan.popular ? 'gradient' : 'outline'} 
              size="md" 
              className="w-full"
            >
              Choose Plan
            </Button>
          </motion.div>
        ))}
      </div>
    </Card>
  );
};

export { PricingCalculator };
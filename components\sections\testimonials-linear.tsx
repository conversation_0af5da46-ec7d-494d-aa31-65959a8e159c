'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Quote, Award, TrendingUp, Users } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Marketing Director",
    company: "Digital Dynamics",
    companySize: "200+ employees",
    industry: "E-commerce",
    content: "ADmyBRAND revolutionized our advertising strategy. We've seen a 300% increase in campaign ROI and our media planning time has been cut by 70%. The platform's reach across all channels is unmatched.",
    avatar: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "300%", metric: "campaign ROI" },
    featured: true
  },
  {
    name: "<PERSON><PERSON>",
    role: "Brand Manager",
    company: "RetailMax",
    companySize: "50-100 employees",
    industry: "Retail",
    content: "The outdoor advertising options on ADmyBRAND are incredible. We booked hoardings across 15 cities in just one day. The real-time availability and pricing transparency saved us weeks of negotiations.",
    avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "15", metric: "cities covered" },
    featured: false
  },
  {
    name: "Amit Patel",
    role: "CEO",
    company: "StartupHub",
    companySize: "10-50 employees",
    industry: "Technology",
    content: "As a startup, budget efficiency is crucial. ADmyBRAND's analytics helped us identify the best performing channels and optimize our ad spend. We achieved 250% better results with 40% less budget.",
    avatar: "https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "250%", metric: "better results" },
    featured: true
  },
  {
    name: "Sneha Reddy",
    role: "Media Planner",
    company: "Creative Agency",
    companySize: "100+ employees",
    industry: "Advertising",
    content: "The variety of media options is outstanding - from traditional hoardings to digital displays, radio to newspapers. ADmyBRAND is our one-stop solution for all client campaigns across India.",
    avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150",
    rating: 5,
    metrics: { increase: "10M+", metric: "ad options" },
    featured: false
  }
];

const stats = [
  { label: "Active Advertisers", value: "10,000+", icon: Users },
  { label: "Campaign Success Rate", value: "95%", icon: TrendingUp },
  { label: "Media Partners", value: "5,000+", icon: Award },
  { label: "Cities Covered", value: "500+", icon: Star }
];

export default function TestimonialsLinear() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.8
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0
    }
  };

  return (
    <section 
      ref={ref}
      className="py-24"
      style={{ 
        background: 'var(--linear-bg-primary)',
        borderTop: '1px solid var(--linear-border)'
      }}
    >
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.div variants={itemVariants} className="inline-flex items-center gap-2 mb-6">
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ background: 'var(--linear-bg-tertiary)' }}>
              <Quote className="w-4 h-4" style={{ color: 'var(--linear-text-secondary)' }} />
            </div>
            <span className="linear-body text-sm font-medium" style={{ color: 'var(--linear-text-secondary)' }}>
              Customer Stories
            </span>
          </motion.div>

          <motion.h2 variants={itemVariants} className="linear-heading-xl mb-4">
            Trusted by India's leading brands
          </motion.h2>
          
          <motion.p variants={itemVariants} className="linear-body-lg max-w-2xl mx-auto">
            See how businesses across India are transforming their advertising with ADmyBRAND's comprehensive platform
          </motion.p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              variants={itemVariants}
              className="linear-card text-center p-6"
            >
              <div className="w-12 h-12 rounded-xl mx-auto mb-4 flex items-center justify-center" style={{ background: 'var(--linear-bg-primary)' }}>
                <stat.icon className="w-6 h-6" style={{ color: 'var(--linear-text-primary)' }} />
              </div>
              <div className="linear-heading-md mb-1">{stat.value}</div>
              <div className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Main Testimonial Carousel */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="relative"
        >
          <div className="linear-card p-8 lg:p-12 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-[linear-gradient(rgba(240,246,252,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(240,246,252,0.03)_1px,transparent_1px)] bg-[size:32px_32px]" />
            </div>

            <div className="relative z-10">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.5 }}
                className="text-center max-w-4xl mx-auto"
              >
                {/* Stars */}
                <div className="flex justify-center gap-1 mb-6">
                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-current" style={{ color: '#F59E0B' }} />
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="linear-heading-lg mb-8 leading-relaxed">
                  "{testimonials[currentIndex].content}"
                </blockquote>

                {/* Metrics */}
                {testimonials[currentIndex].metrics && (
                  <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full mb-8" style={{ background: 'var(--linear-bg-primary)', border: '1px solid var(--linear-border)' }}>
                    <TrendingUp className="w-4 h-4" style={{ color: '#10B981' }} />
                    <span className="linear-body-sm font-medium">
                      <span style={{ color: '#10B981' }}>{testimonials[currentIndex].metrics.increase}</span>
                      <span style={{ color: 'var(--linear-text-secondary)' }}> {testimonials[currentIndex].metrics.metric}</span>
                    </span>
                  </div>
                )}

                {/* Author */}
                <div className="flex items-center justify-center gap-4">
                  <img
                    src={testimonials[currentIndex].avatar}
                    alt={testimonials[currentIndex].name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className="text-left">
                    <div className="linear-body font-semibold">{testimonials[currentIndex].name}</div>
                    <div className="linear-body-sm" style={{ color: 'var(--linear-text-secondary)' }}>
                      {testimonials[currentIndex].role} at {testimonials[currentIndex].company}
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Navigation */}
            <div className="flex justify-center gap-4 mt-8">
              <button
                onClick={prevTestimonial}
                className="w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105"
                style={{ 
                  background: 'var(--linear-bg-tertiary)', 
                  border: '1px solid var(--linear-border)',
                  color: 'var(--linear-text-secondary)'
                }}
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <button
                onClick={nextTestimonial}
                className="w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105"
                style={{ 
                  background: 'var(--linear-bg-tertiary)', 
                  border: '1px solid var(--linear-border)',
                  color: 'var(--linear-text-secondary)'
                }}
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>

            {/* Dots */}
            <div className="flex justify-center gap-2 mt-6">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentIndex 
                      ? 'w-6' 
                      : 'opacity-50 hover:opacity-75'
                  }`}
                  style={{ 
                    background: index === currentIndex 
                      ? 'var(--linear-text-primary)' 
                      : 'var(--linear-text-secondary)'
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
